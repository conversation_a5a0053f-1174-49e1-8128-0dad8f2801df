-- Sample Data für CrowdSec Dashboard Testing

-- <PERSON><PERSON> Scenarios
INSERT INTO scenarios (name, description, author, version, labels, is_enabled) VALUES
('crowdsecurity/ssh-bf', 'SSH Brute Force Detection', 'crowdsecurity', '1.0', '{"service": "ssh", "type": "bruteforce"}', true),
('crowdsecurity/http-bf', 'HTTP Brute Force Detection', 'crowdsecurity', '1.0', '{"service": "http", "type": "bruteforce"}', true),
('crowdsecurity/http-crawl-non_statics', 'HTTP Crawling Detection', 'crowdsecurity', '1.0', '{"service": "http", "type": "crawling"}', true),
('crowdsecurity/http-probing', 'HTTP Probing Detection', 'crowdsecurity', '1.0', '{"service": "http", "type": "probing"}', true),
('crowdsecurity/iptables-scan-multi_ports', 'Port Scan Detection', 'crowdsecurity', '1.0', '{"service": "iptables", "type": "scan"}', true);

-- <PERSON><PERSON> Parsers
INSERT INTO parsers (name, description, author, version, stage, is_enabled) VALUES
('crowdsecurity/syslog-logs', 'Syslog Parser', 'crowdsecurity', '1.0', 's00-raw', true),
('crowdsecurity/dateparse-enrich', 'Date Parsing Enrichment', 'crowdsecurity', '1.0', 's01-parse', true),
('crowdsecurity/geoip-enrich', 'GeoIP Enrichment', 'crowdsecurity', '1.0', 's02-enrich', true),
('crowdsecurity/http-logs', 'HTTP Access Logs Parser', 'crowdsecurity', '1.0', 's01-parse', true),
('crowdsecurity/ssh-logs', 'SSH Logs Parser', 'crowdsecurity', '1.0', 's01-parse', true);

-- Sample Collections
INSERT INTO collections (name, description, author, version, parsers, scenarios, is_enabled) VALUES
('crowdsecurity/linux', 'Linux Base Collection', 'crowdsecurity', '1.0', 
 ARRAY['crowdsecurity/syslog-logs', 'crowdsecurity/dateparse-enrich'], 
 ARRAY['crowdsecurity/ssh-bf'], true),
('crowdsecurity/nginx', 'Nginx Collection', 'crowdsecurity', '1.0',
 ARRAY['crowdsecurity/http-logs', 'crowdsecurity/geoip-enrich'],
 ARRAY['crowdsecurity/http-bf', 'crowdsecurity/http-crawl-non_statics'], true);

-- Sample Bouncers
INSERT INTO bouncers (name, type, description, ip_address, version, last_pull, is_active) VALUES
('haproxy-bouncer-1', 'haproxy', 'Main HAProxy Bouncer', '***********', '0.0.6', NOW() - INTERVAL '2 minutes', true),
('nginx-bouncer-1', 'nginx', 'Nginx Bouncer for Web Services', '***********', '0.0.4', NOW() - INTERVAL '5 minutes', true),
('firewall-bouncer-1', 'firewall', 'IPTables Firewall Bouncer', '***********', '0.0.8', NOW() - INTERVAL '1 minute', true);

-- Sample Alerts (letzte 24 Stunden)
INSERT INTO alerts (crowdsec_id, scenario, message, source_ip, source_country, source_as_name, events_count, start_at, stop_at, severity) VALUES
('alert-001', 'crowdsecurity/ssh-bf', 'SSH Brute Force from *************', '*************', 'DE', 'Deutsche Telekom AG', 15, NOW() - INTERVAL '2 hours', NOW() - INTERVAL '1 hour 30 minutes', 'high'),
('alert-002', 'crowdsecurity/http-bf', 'HTTP Brute Force on /wp-admin', '************', 'CN', 'China Telecom', 25, NOW() - INTERVAL '4 hours', NOW() - INTERVAL '3 hours', 'high'),
('alert-003', 'crowdsecurity/http-crawl-non_statics', 'Aggressive crawling detected', '*************', 'US', 'Amazon Technologies Inc.', 8, NOW() - INTERVAL '1 hour', NULL, 'medium'),
('alert-004', 'crowdsecurity/http-probing', 'HTTP probing attempt', '************', 'RU', 'Rostelecom', 5, NOW() - INTERVAL '30 minutes', NULL, 'medium'),
('alert-005', 'crowdsecurity/iptables-scan-multi_ports', 'Port scan detected', '**********', 'FR', 'Orange S.A.', 12, NOW() - INTERVAL '6 hours', NOW() - INTERVAL '5 hours', 'high');

-- Sample Decisions basierend auf Alerts
INSERT INTO decisions (crowdsec_id, alert_id, type, scope, value, duration, scenario, until) VALUES
('decision-001', (SELECT id FROM alerts WHERE crowdsec_id = 'alert-001'), 'ban', 'ip', '*************', '4h', 'crowdsecurity/ssh-bf', NOW() + INTERVAL '2 hours'),
('decision-002', (SELECT id FROM alerts WHERE crowdsec_id = 'alert-002'), 'ban', 'ip', '************', '24h', 'crowdsecurity/http-bf', NOW() + INTERVAL '20 hours'),
('decision-003', (SELECT id FROM alerts WHERE crowdsec_id = 'alert-003'), 'captcha', 'ip', '*************', '2h', 'crowdsecurity/http-crawl-non_statics', NOW() + INTERVAL '1 hour 30 minutes'),
('decision-004', (SELECT id FROM alerts WHERE crowdsec_id = 'alert-004'), 'throttle', 'ip', '************', '1h', 'crowdsecurity/http-probing', NOW() + INTERVAL '30 minutes'),
('decision-005', (SELECT id FROM alerts WHERE crowdsec_id = 'alert-005'), 'ban', 'ip', '**********', '12h', 'crowdsecurity/iptables-scan-multi_ports', NOW() + INTERVAL '6 hours');

-- Sample Bouncer Metrics (letzte Stunden)
INSERT INTO bouncer_metrics (bouncer_id, requests_total, requests_blocked, requests_allowed, response_time_avg, recorded_at) VALUES
((SELECT id FROM bouncers WHERE name = 'haproxy-bouncer-1'), 15420, 234, 15186, 0.045, NOW() - INTERVAL '1 hour'),
((SELECT id FROM bouncers WHERE name = 'haproxy-bouncer-1'), 16890, 267, 16623, 0.042, NOW() - INTERVAL '30 minutes'),
((SELECT id FROM bouncers WHERE name = 'haproxy-bouncer-1'), 18234, 289, 17945, 0.048, NOW()),
((SELECT id FROM bouncers WHERE name = 'nginx-bouncer-1'), 8920, 145, 8775, 0.032, NOW() - INTERVAL '1 hour'),
((SELECT id FROM bouncers WHERE name = 'nginx-bouncer-1'), 9456, 167, 9289, 0.035, NOW() - INTERVAL '30 minutes'),
((SELECT id FROM bouncers WHERE name = 'nginx-bouncer-1'), 10123, 178, 9945, 0.031, NOW());

-- Sample Engine Metrics
INSERT INTO engine_metrics (parsers_loaded, scenarios_loaded, buckets_current, buckets_overflow, alerts_generated, decisions_created, log_lines_processed, log_lines_parsed, log_lines_unparsed, uptime_seconds, recorded_at) VALUES
(12, 25, 45, 5, 127, 89, 125430, 124890, 540, 86400, NOW() - INTERVAL '1 hour'),
(12, 25, 48, 8, 132, 94, 142350, 141720, 630, 90000, NOW() - INTERVAL '30 minutes'),
(12, 25, 42, 3, 135, 97, 156780, 156120, 660, 93600, NOW());

-- Sample HAProxy Metrics
INSERT INTO haproxy_metrics (frontend_name, backend_name, requests_total, requests_blocked, bytes_in, bytes_out, response_time_avg, active_sessions, status, recorded_at) VALUES
('https_frontend', 'web_backend', 45230, 234, 125430000, 890450000, 0.125, 45, 'UP', NOW() - INTERVAL '1 hour'),
('https_frontend', 'web_backend', 48920, 267, 134560000, 945670000, 0.118, 52, 'UP', NOW() - INTERVAL '30 minutes'),
('https_frontend', 'web_backend', 52340, 289, 145890000, 1023450000, 0.132, 38, 'UP', NOW()),
('api_frontend', 'api_backend', 12450, 45, 23450000, 156780000, 0.089, 12, 'UP', NOW() - INTERVAL '1 hour'),
('api_frontend', 'api_backend', 13890, 52, 26780000, 178920000, 0.095, 15, 'UP', NOW() - INTERVAL '30 minutes'),
('api_frontend', 'api_backend', 15230, 58, 29340000, 198450000, 0.087, 18, 'UP', NOW());

-- Sample System Events
INSERT INTO system_events (event_type, severity, message, details, source) VALUES
('bouncer_offline', 'high', 'Bouncer nginx-bouncer-2 went offline', '{"bouncer_name": "nginx-bouncer-2", "last_seen": "2024-01-15T10:30:00Z"}', 'bouncer_monitor'),
('high_alert_rate', 'medium', 'Alert rate increased by 150% in last hour', '{"current_rate": 25, "previous_rate": 10, "threshold": 20}', 'alert_monitor'),
('decision_cleanup', 'low', 'Cleaned up 45 expired decisions', '{"expired_count": 45, "total_active": 234}', 'cleanup_service'),
('config_reload', 'low', 'CrowdSec configuration reloaded successfully', '{"parsers": 12, "scenarios": 25}', 'crowdsec_service'),
('database_backup', 'low', 'Daily database backup completed', '{"backup_size": "2.3GB", "duration": "45s"}', 'backup_service');
