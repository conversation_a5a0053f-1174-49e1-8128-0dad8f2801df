-- Lokale Test-Datenbank Setup
-- Die<PERSON> Script erstellt eine lokale SQLite-Datenbank für Tests

-- F<PERSON>r lokale Tests mit SQLite
-- Führe aus: sqlite3 test_crowdsec_dashboard.db < database/test-setup.sql

-- Vereinfachtes Schema für SQLite (ohne PostgreSQL-spezifische Features)

-- Alerts Table
CREATE TABLE IF NOT EXISTS alerts (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    crowdsec_id TEXT UNIQUE NOT NULL,
    scenario TEXT NOT NULL,
    message TEXT,
    source_ip TEXT,
    source_country TEXT,
    source_as_name TEXT,
    source_as_number INTEGER,
    events_count INTEGER DEFAULT 1,
    capacity INTEGER,
    leakspeed TEXT,
    simulated BOOLEAN DEFAULT 0,
    start_at DATETIME NOT NULL,
    stop_at DATETIME,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'ignored')),
    severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    machine_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Decisions Table
CREATE TABLE IF NOT EXISTS decisions (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    crowdsec_id TEXT UNIQUE NOT NULL,
    alert_id TEXT REFERENCES alerts(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('ban', 'captcha', 'throttle', 'allow')),
    scope TEXT NOT NULL CHECK (scope IN ('ip', 'range', 'country', 'as')),
    value TEXT NOT NULL,
    duration TEXT,
    scenario TEXT,
    origin TEXT DEFAULT 'crowdsec',
    simulated BOOLEAN DEFAULT 0,
    until DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME
);

-- Bouncers Table
CREATE TABLE IF NOT EXISTS bouncers (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    name TEXT UNIQUE NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('haproxy', 'nginx', 'apache', 'firewall', 'cloudflare')),
    description TEXT,
    ip_address TEXT,
    version TEXT,
    last_pull DATETIME,
    auth_type TEXT DEFAULT 'api_key',
    api_key_hash TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Bouncer Metrics Table
CREATE TABLE IF NOT EXISTS bouncer_metrics (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    bouncer_id TEXT REFERENCES bouncers(id) ON DELETE CASCADE,
    requests_total INTEGER DEFAULT 0,
    requests_blocked INTEGER DEFAULT 0,
    requests_allowed INTEGER DEFAULT 0,
    response_time_avg REAL,
    cpu_usage REAL,
    memory_usage INTEGER,
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Engine Metrics Table
CREATE TABLE IF NOT EXISTS engine_metrics (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    parsers_loaded INTEGER,
    scenarios_loaded INTEGER,
    buckets_current INTEGER,
    buckets_overflow INTEGER,
    alerts_generated INTEGER,
    decisions_created INTEGER,
    log_lines_processed INTEGER,
    log_lines_parsed INTEGER,
    log_lines_unparsed INTEGER,
    cpu_usage REAL,
    memory_usage INTEGER,
    uptime_seconds INTEGER,
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- HAProxy Metrics Table
CREATE TABLE IF NOT EXISTS haproxy_metrics (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    frontend_name TEXT,
    backend_name TEXT,
    server_name TEXT,
    requests_total INTEGER DEFAULT 0,
    requests_blocked INTEGER DEFAULT 0,
    bytes_in INTEGER DEFAULT 0,
    bytes_out INTEGER DEFAULT 0,
    response_time_avg REAL,
    active_sessions INTEGER DEFAULT 0,
    max_sessions INTEGER DEFAULT 0,
    status TEXT,
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- System Events Table
CREATE TABLE IF NOT EXISTS system_events (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    event_type TEXT NOT NULL,
    severity TEXT DEFAULT 'low' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    message TEXT NOT NULL,
    details TEXT, -- JSON als TEXT gespeichert
    source TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes für Performance
CREATE INDEX IF NOT EXISTS idx_alerts_start_at ON alerts(start_at);
CREATE INDEX IF NOT EXISTS idx_alerts_source_ip ON alerts(source_ip);
CREATE INDEX IF NOT EXISTS idx_alerts_scenario ON alerts(scenario);
CREATE INDEX IF NOT EXISTS idx_alerts_status ON alerts(status);

CREATE INDEX IF NOT EXISTS idx_decisions_value ON decisions(value);
CREATE INDEX IF NOT EXISTS idx_decisions_type ON decisions(type);
CREATE INDEX IF NOT EXISTS idx_decisions_until ON decisions(until);
CREATE INDEX IF NOT EXISTS idx_decisions_created_at ON decisions(created_at);

CREATE INDEX IF NOT EXISTS idx_bouncer_metrics_recorded_at ON bouncer_metrics(recorded_at);
CREATE INDEX IF NOT EXISTS idx_engine_metrics_recorded_at ON engine_metrics(recorded_at);
CREATE INDEX IF NOT EXISTS idx_haproxy_metrics_recorded_at ON haproxy_metrics(recorded_at);

-- Trigger für updated_at (SQLite Version)
CREATE TRIGGER IF NOT EXISTS update_alerts_updated_at 
    AFTER UPDATE ON alerts
    FOR EACH ROW
    BEGIN
        UPDATE alerts SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_decisions_updated_at 
    AFTER UPDATE ON decisions
    FOR EACH ROW
    BEGIN
        UPDATE decisions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_bouncers_updated_at 
    AFTER UPDATE ON bouncers
    FOR EACH ROW
    BEGIN
        UPDATE bouncers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Views für häufige Abfragen
CREATE VIEW IF NOT EXISTS active_decisions AS
SELECT d.*, a.scenario, a.source_country, a.severity
FROM decisions d
LEFT JOIN alerts a ON d.alert_id = a.id
WHERE d.deleted_at IS NULL 
  AND (d.until IS NULL OR d.until > datetime('now'));

CREATE VIEW IF NOT EXISTS recent_alerts AS
SELECT *
FROM alerts
WHERE start_at >= datetime('now', '-24 hours')
ORDER BY start_at DESC;

CREATE VIEW IF NOT EXISTS bouncer_status AS
SELECT 
    b.*,
    CASE 
        WHEN b.last_pull > datetime('now', '-5 minutes') THEN 'online'
        WHEN b.last_pull > datetime('now', '-1 hour') THEN 'warning'
        ELSE 'offline'
    END as status
FROM bouncers b
WHERE b.is_active = 1;
