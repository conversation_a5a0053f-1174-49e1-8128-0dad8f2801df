-- CrowdSec Dashboard Database Schema
-- PostgreSQL Schema für historische Daten und Dashboard-Funktionalität

-- Extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Enum Types
CREATE TYPE alert_status AS ENUM ('active', 'resolved', 'ignored');
CREATE TYPE decision_type AS ENUM ('ban', 'captcha', 'throttle', 'allow');
CREATE TYPE decision_scope AS ENUM ('ip', 'range', 'country', 'as');
CREATE TYPE bouncer_type AS ENUM ('haproxy', 'nginx', 'apache', 'firewall', 'cloudflare');
CREATE TYPE severity_level AS ENUM ('low', 'medium', 'high', 'critical');

-- Alerts Table - Historische CrowdSec Alerts
CREATE TABLE alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    crowdsec_id VARCHAR(255) UNIQUE NOT NULL,
    scenario VARCHAR(255) NOT NULL,
    message TEXT,
    source_ip INET,
    source_country VARCHAR(2),
    source_as_name VARCHAR(255),
    source_as_number INTEGER,
    events_count INTEGER DEFAULT 1,
    capacity INTEGER,
    leakspeed VARCHAR(50),
    simulated BOOLEAN DEFAULT FALSE,
    start_at TIMESTAMP WITH TIME ZONE NOT NULL,
    stop_at TIMESTAMP WITH TIME ZONE,
    status alert_status DEFAULT 'active',
    severity severity_level DEFAULT 'medium',
    machine_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Decisions Table - CrowdSec Decisions (aktiv und historisch)
CREATE TABLE decisions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    crowdsec_id VARCHAR(255) UNIQUE NOT NULL,
    alert_id UUID REFERENCES alerts(id) ON DELETE CASCADE,
    type decision_type NOT NULL,
    scope decision_scope NOT NULL,
    value VARCHAR(255) NOT NULL,
    duration VARCHAR(50),
    scenario VARCHAR(255),
    origin VARCHAR(50) DEFAULT 'crowdsec',
    simulated BOOLEAN DEFAULT FALSE,
    until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Bouncers Table - Registrierte Bouncers
CREATE TABLE bouncers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    type bouncer_type NOT NULL,
    description TEXT,
    ip_address INET,
    version VARCHAR(50),
    last_pull TIMESTAMP WITH TIME ZONE,
    auth_type VARCHAR(50) DEFAULT 'api_key',
    api_key_hash VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bouncer Metrics - Performance Metriken der Bouncers
CREATE TABLE bouncer_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bouncer_id UUID REFERENCES bouncers(id) ON DELETE CASCADE,
    requests_total BIGINT DEFAULT 0,
    requests_blocked BIGINT DEFAULT 0,
    requests_allowed BIGINT DEFAULT 0,
    response_time_avg DECIMAL(10,3),
    cpu_usage DECIMAL(5,2),
    memory_usage BIGINT,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Engine Metrics - CrowdSec Engine Performance
CREATE TABLE engine_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    parsers_loaded INTEGER,
    scenarios_loaded INTEGER,
    buckets_current INTEGER,
    buckets_overflow INTEGER,
    alerts_generated INTEGER,
    decisions_created INTEGER,
    log_lines_processed BIGINT,
    log_lines_parsed BIGINT,
    log_lines_unparsed BIGINT,
    cpu_usage DECIMAL(5,2),
    memory_usage BIGINT,
    uptime_seconds BIGINT,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- HAProxy Metrics - HAProxy Statistiken
CREATE TABLE haproxy_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    frontend_name VARCHAR(255),
    backend_name VARCHAR(255),
    server_name VARCHAR(255),
    requests_total BIGINT DEFAULT 0,
    requests_blocked BIGINT DEFAULT 0,
    bytes_in BIGINT DEFAULT 0,
    bytes_out BIGINT DEFAULT 0,
    response_time_avg DECIMAL(10,3),
    active_sessions INTEGER DEFAULT 0,
    max_sessions INTEGER DEFAULT 0,
    status VARCHAR(50),
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Scenarios Table - CrowdSec Scenarios Information
CREATE TABLE scenarios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    author VARCHAR(255),
    version VARCHAR(50),
    labels JSONB,
    scenario_references TEXT[],
    is_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Parsers Table - CrowdSec Parsers Information
CREATE TABLE parsers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    author VARCHAR(255),
    version VARCHAR(50),
    stage VARCHAR(50),
    is_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Collections Table - CrowdSec Collections
CREATE TABLE collections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    author VARCHAR(255),
    version VARCHAR(50),
    parsers TEXT[],
    scenarios TEXT[],
    postoverflows TEXT[],
    is_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System Events - Dashboard System Events
CREATE TABLE system_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(100) NOT NULL,
    severity severity_level DEFAULT 'low',
    message TEXT NOT NULL,
    details JSONB,
    source VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes für Performance
CREATE INDEX idx_alerts_start_at ON alerts(start_at);
CREATE INDEX idx_alerts_source_ip ON alerts(source_ip);
CREATE INDEX idx_alerts_scenario ON alerts(scenario);
CREATE INDEX idx_alerts_status ON alerts(status);
CREATE INDEX idx_alerts_severity ON alerts(severity);

CREATE INDEX idx_decisions_value ON decisions(value);
CREATE INDEX idx_decisions_type ON decisions(type);
CREATE INDEX idx_decisions_scope ON decisions(scope);
CREATE INDEX idx_decisions_until ON decisions(until);
CREATE INDEX idx_decisions_created_at ON decisions(created_at);

CREATE INDEX idx_bouncer_metrics_recorded_at ON bouncer_metrics(recorded_at);
CREATE INDEX idx_bouncer_metrics_bouncer_id ON bouncer_metrics(bouncer_id);

CREATE INDEX idx_engine_metrics_recorded_at ON engine_metrics(recorded_at);
CREATE INDEX idx_haproxy_metrics_recorded_at ON haproxy_metrics(recorded_at);

CREATE INDEX idx_system_events_created_at ON system_events(created_at);
CREATE INDEX idx_system_events_event_type ON system_events(event_type);

-- Trigger für updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_alerts_updated_at BEFORE UPDATE ON alerts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_decisions_updated_at BEFORE UPDATE ON decisions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bouncers_updated_at BEFORE UPDATE ON bouncers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views für häufige Abfragen
CREATE VIEW active_decisions AS
SELECT d.*, a.source_country, a.severity
FROM decisions d
LEFT JOIN alerts a ON d.alert_id = a.id
WHERE d.deleted_at IS NULL 
  AND (d.until IS NULL OR d.until > NOW());

CREATE VIEW recent_alerts AS
SELECT *
FROM alerts
WHERE start_at >= NOW() - INTERVAL '24 hours'
ORDER BY start_at DESC;

CREATE VIEW bouncer_status AS
SELECT 
    b.*,
    CASE 
        WHEN b.last_pull > NOW() - INTERVAL '5 minutes' THEN 'online'
        WHEN b.last_pull > NOW() - INTERVAL '1 hour' THEN 'warning'
        ELSE 'offline'
    END as status
FROM bouncers b
WHERE b.is_active = TRUE;
