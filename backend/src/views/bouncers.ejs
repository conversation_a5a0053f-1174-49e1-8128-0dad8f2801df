<!-- Bouncers Page -->
<div class="mb-6">
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">Bouncers</h2>
        <button onclick="syncBouncers()" class="btn-primary">
            <i class="fas fa-sync mr-2"></i>
            Sync Bouncers
        </button>
    </div>
    <p class="text-gray-600 mt-2">Monitor CrowdSec bouncers and their status</p>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-success-100 rounded-lg">
                        <i class="fas fa-server text-success-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Bouncers</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="active-bouncers">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-primary-100 rounded-lg">
                        <i class="fas fa-shield-alt text-primary-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Requests</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="total-requests">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-danger-100 rounded-lg">
                        <i class="fas fa-ban text-danger-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Blocked Requests</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="blocked-requests">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-warning-100 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-warning-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Offline Bouncers</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="offline-bouncers">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bouncers Table -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Registered Bouncers</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Version</th>
                    <th>Status</th>
                    <th>Last Seen</th>
                    <th>IP Address</th>
                    <th>Requests</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="bouncers-table-body">
                <!-- Loading skeleton -->
                <tr>
                    <td colspan="8" class="text-center py-8">
                        <div class="animate-pulse">
                            <div class="h-4 bg-gray-300 rounded w-1/4 mx-auto mb-2"></div>
                            <div class="h-4 bg-gray-300 rounded w-1/2 mx-auto"></div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadBouncersData();
});

async function loadBouncersData() {
    try {
        // Load stats
        const statsResponse = await fetch('/api/bouncers/stats');
        if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            updateBouncersStats(statsData.data);
        }

        // Load bouncers table
        const bouncersResponse = await fetch('/api/bouncers');
        if (bouncersResponse.ok) {
            const bouncersData = await bouncersResponse.json();
            updateBouncersTable(bouncersData.data);
        }
    } catch (error) {
        console.error('Error loading bouncers data:', error);
    }
}

function updateBouncersStats(stats) {
    document.getElementById('active-bouncers').textContent = stats?.active || 0;
    document.getElementById('total-requests').textContent = stats?.totalRequests || 0;
    document.getElementById('blocked-requests').textContent = stats?.blockedRequests || 0;
    document.getElementById('offline-bouncers').textContent = stats?.offline || 0;
}

function updateBouncersTable(bouncers) {
    const tbody = document.getElementById('bouncers-table-body');
    if (!bouncers || bouncers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center py-8 text-gray-500">No bouncers found</td></tr>';
        return;
    }

    tbody.innerHTML = bouncers.map(bouncer => {
        const isOnline = bouncer.last_pull && new Date(bouncer.last_pull) > new Date(Date.now() - 5 * 60 * 1000);
        return `
            <tr class="hover:bg-gray-50">
                <td class="font-medium">${bouncer.name || 'Unknown'}</td>
                <td>${bouncer.type || 'Unknown'}</td>
                <td>${bouncer.version || 'Unknown'}</td>
                <td>
                    <span class="status-badge ${isOnline ? 'status-success' : 'status-danger'}">
                        <i class="fas fa-${isOnline ? 'check-circle' : 'times-circle'} mr-1"></i>
                        ${isOnline ? 'Online' : 'Offline'}
                    </span>
                </td>
                <td class="timestamp">${formatTimestamp(bouncer.last_pull)}</td>
                <td><span class="ip-address">${bouncer.ip_address || 'Unknown'}</span></td>
                <td>${bouncer.requests_count || 0}</td>
                <td>
                    <button onclick="viewBouncer('${bouncer.name}')" class="text-primary-600 hover:text-primary-500 text-sm mr-2">
                        <i class="fas fa-eye mr-1"></i>
                        View
                    </button>
                    <button onclick="deleteBouncer('${bouncer.name}')" class="text-danger-600 hover:text-danger-500 text-sm">
                        <i class="fas fa-trash mr-1"></i>
                        Delete
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

async function syncBouncers() {
    try {
        const response = await fetch('/api/bouncers/sync', { method: 'POST' });
        if (response.ok) {
            showNotification('Bouncers synced successfully', 'success');
            loadBouncersData();
        } else {
            showNotification('Failed to sync bouncers', 'error');
        }
    } catch (error) {
        showNotification('Error syncing bouncers', 'error');
    }
}

function viewBouncer(bouncerName) {
    // Mock bouncer detail view
    showNotification('Bouncer detail view coming soon', 'info');
}

function deleteBouncer(bouncerName) {
    if (confirm(`Are you sure you want to delete bouncer "${bouncerName}"?`)) {
        // Mock delete functionality
        showNotification('Delete functionality coming soon', 'info');
    }
}
</script>
