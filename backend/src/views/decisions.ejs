<!-- Decisions Page -->
<div class="mb-6">
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">Security Decisions</h2>
        <button onclick="syncDecisions()" class="btn-primary">
            <i class="fas fa-sync mr-2"></i>
            Sync Decisions
        </button>
    </div>
    <p class="text-gray-600 mt-2">View and manage CrowdSec security decisions</p>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-primary-100 rounded-lg">
                        <i class="fas fa-gavel text-primary-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Decisions</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="active-decisions">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-danger-100 rounded-lg">
                        <i class="fas fa-ban text-danger-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Banned IPs</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="banned-ips">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-warning-100 rounded-lg">
                        <i class="fas fa-clock text-warning-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Expiring Soon</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="expiring-decisions">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-success-100 rounded-lg">
                        <i class="fas fa-check-circle text-success-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Expired Today</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="expired-today">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Decisions Table -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Active Decisions</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="table">
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Value</th>
                    <th>Scenario</th>
                    <th>Duration</th>
                    <th>Origin</th>
                    <th>Created</th>
                    <th>Expires</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="decisions-table-body">
                <!-- Loading skeleton -->
                <tr>
                    <td colspan="8" class="text-center py-8">
                        <div class="animate-pulse">
                            <div class="h-4 bg-gray-300 rounded w-1/4 mx-auto mb-2"></div>
                            <div class="h-4 bg-gray-300 rounded w-1/2 mx-auto"></div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadDecisionsData();
});

async function loadDecisionsData() {
    try {
        // Load stats
        const statsResponse = await fetch('/api/decisions/stats');
        if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            updateDecisionsStats(statsData.data);
        }

        // Load decisions table
        const decisionsResponse = await fetch('/api/decisions');
        if (decisionsResponse.ok) {
            const decisionsData = await decisionsResponse.json();
            updateDecisionsTable(decisionsData.data);
        }
    } catch (error) {
        console.error('Error loading decisions data:', error);
    }
}

function updateDecisionsStats(stats) {
    document.getElementById('active-decisions').textContent = stats?.active || 0;
    document.getElementById('banned-ips').textContent = stats?.bannedIps || 0;
    document.getElementById('expiring-decisions').textContent = stats?.expiringSoon || 0;
    document.getElementById('expired-today').textContent = stats?.expiredToday || 0;
}

function updateDecisionsTable(decisions) {
    const tbody = document.getElementById('decisions-table-body');
    if (!decisions || decisions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center py-8 text-gray-500">No decisions found</td></tr>';
        return;
    }

    tbody.innerHTML = decisions.map(decision => `
        <tr class="hover:bg-gray-50">
            <td>
                <span class="status-badge ${decision.type === 'ban' ? 'status-danger' : 'status-warning'}">
                    ${decision.type || 'unknown'}
                </span>
            </td>
            <td><span class="ip-address">${decision.value || 'Unknown'}</span></td>
            <td class="font-medium">${decision.scenario || 'Unknown'}</td>
            <td>${decision.duration || 'Unknown'}</td>
            <td>${decision.origin || 'Unknown'}</td>
            <td class="timestamp">${formatTimestamp(decision.created_at)}</td>
            <td class="timestamp">${formatTimestamp(decision.until)}</td>
            <td>
                <button onclick="deleteDecision('${decision.id}')" class="text-danger-600 hover:text-danger-500 text-sm">
                    <i class="fas fa-trash mr-1"></i>
                    Delete
                </button>
            </td>
        </tr>
    `).join('');
}

async function syncDecisions() {
    try {
        const response = await fetch('/api/decisions/sync', { method: 'POST' });
        if (response.ok) {
            showNotification('Decisions synced successfully', 'success');
            loadDecisionsData();
        } else {
            showNotification('Failed to sync decisions', 'error');
        }
    } catch (error) {
        showNotification('Error syncing decisions', 'error');
    }
}

function deleteDecision(decisionId) {
    if (confirm('Are you sure you want to delete this decision?')) {
        // Mock delete functionality
        showNotification('Delete functionality coming soon', 'info');
    }
}
</script>
