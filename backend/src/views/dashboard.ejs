<!-- Stats Cards -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-danger-100 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-danger-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Alerts</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="alerts-count">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-primary-100 rounded-lg">
                        <i class="fas fa-gavel text-primary-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Decisions</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="decisions-count">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-success-100 rounded-lg">
                        <i class="fas fa-server text-success-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Bouncers</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="bouncers-count">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-warning-100 rounded-lg">
                        <i class="fas fa-chart-bar text-warning-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Blocked IPs (24h)</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="blocked-ips-count">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Tables -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Recent Alerts -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Alerts</h3>
        </div>
        <div class="p-6">
            <div id="recent-alerts" class="space-y-4">
                <!-- Loading skeleton -->
                <div class="animate-pulse space-y-3">
                    <div class="flex items-center space-x-3">
                        <div class="h-8 w-8 bg-gray-300 rounded-full"></div>
                        <div class="flex-1 space-y-2">
                            <div class="h-4 bg-gray-300 rounded w-3/4"></div>
                            <div class="h-3 bg-gray-300 rounded w-1/2"></div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="h-8 w-8 bg-gray-300 rounded-full"></div>
                        <div class="flex-1 space-y-2">
                            <div class="h-4 bg-gray-300 rounded w-3/4"></div>
                            <div class="h-3 bg-gray-300 rounded w-1/2"></div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="h-8 w-8 bg-gray-300 rounded-full"></div>
                        <div class="flex-1 space-y-2">
                            <div class="h-4 bg-gray-300 rounded w-3/4"></div>
                            <div class="h-3 bg-gray-300 rounded w-1/2"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-6">
                <a href="/alerts" class="text-primary-600 hover:text-primary-500 text-sm font-medium">
                    View all alerts <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">System Status</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4" id="system-status">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">CrowdSec Engine</span>
                    <span class="status-badge status-loading">
                        <div class="animate-pulse bg-gray-300 h-4 w-16 rounded"></div>
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">HAProxy</span>
                    <span class="status-badge status-loading">
                        <div class="animate-pulse bg-gray-300 h-4 w-16 rounded"></div>
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Database</span>
                    <span class="status-badge status-loading">
                        <div class="animate-pulse bg-gray-300 h-4 w-16 rounded"></div>
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">API Server</span>
                    <span class="status-badge status-success">
                        <i class="fas fa-check-circle text-success-500 mr-1"></i>
                        Healthy
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alert Trends Chart -->
<div class="bg-white shadow rounded-lg mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Alert Trends (Last 7 Days)</h3>
    </div>
    <div class="p-6">
        <div class="h-64">
            <canvas id="alertTrendsChart"></canvas>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <button onclick="syncAlerts()" class="btn-primary">
                <i class="fas fa-sync mr-2"></i>
                Sync Alerts
            </button>
            <button onclick="syncDecisions()" class="btn-secondary">
                <i class="fas fa-gavel mr-2"></i>
                Sync Decisions
            </button>
            <button onclick="syncBouncers()" class="btn-secondary">
                <i class="fas fa-server mr-2"></i>
                Sync Bouncers
            </button>
            <button onclick="exportData()" class="btn-secondary">
                <i class="fas fa-download mr-2"></i>
                Export Data
            </button>
        </div>
    </div>
</div>
