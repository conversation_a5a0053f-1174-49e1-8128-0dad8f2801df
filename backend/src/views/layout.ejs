<!DOCTYPE html>
<html lang="de" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - CrowdSec Dashboard</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/dashboard.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="h-full bg-gray-50">
    <div class="h-screen flex overflow-hidden">
        <!-- Sidebar -->
        <div class="hidden lg:flex lg:flex-shrink-0">
            <div class="flex flex-col w-64">
                <div class="flex flex-col h-0 flex-1 bg-white shadow-lg">
                    <!-- Logo -->
                    <div class="flex items-center h-16 flex-shrink-0 px-6 bg-primary-600">
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt text-white text-2xl"></i>
                            <div class="ml-3">
                                <h1 class="text-lg font-semibold text-white">CrowdSec</h1>
                                <p class="text-xs text-primary-200">Dashboard</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Navigation -->
                    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                        <a href="/" class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                        <a href="/alerts" class="nav-link <%= currentPage === 'alerts' ? 'active' : '' %>">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Alerts</span>
                        </a>
                        <a href="/decisions" class="nav-link <%= currentPage === 'decisions' ? 'active' : '' %>">
                            <i class="fas fa-gavel"></i>
                            <span>Decisions</span>
                        </a>
                        <a href="/bouncers" class="nav-link <%= currentPage === 'bouncers' ? 'active' : '' %>">
                            <i class="fas fa-server"></i>
                            <span>Bouncers</span>
                        </a>
                        <a href="/haproxy" class="nav-link <%= currentPage === 'haproxy' ? 'active' : '' %>">
                            <i class="fas fa-chart-bar"></i>
                            <span>HAProxy</span>
                        </a>
                    </nav>
                    
                    <!-- Footer -->
                    <div class="flex-shrink-0 p-4 border-t border-gray-200">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                                    <span class="text-xs font-medium text-primary-600">CS</span>
                                </div>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-700">CrowdSec</p>
                                <p class="text-xs text-gray-500">v1.6.11</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex flex-col w-0 flex-1 overflow-hidden">
            <!-- Top bar -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
                    <div class="flex items-center">
                        <button class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100" onclick="toggleSidebar()">
                            <i class="fas fa-bars h-6 w-6"></i>
                        </button>
                        <div class="ml-4 lg:ml-0">
                            <h1 class="text-xl font-semibold text-gray-900"><%= title %></h1>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- Status indicator -->
                        <div class="hidden sm:flex items-center space-x-2">
                            <div class="flex items-center">
                                <div class="h-2 w-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="ml-2 text-sm text-gray-600">Online</span>
                            </div>
                        </div>
                        
                        <!-- Refresh button -->
                        <button onclick="refreshData()" class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-sync-alt h-5 w-5"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Page content -->
            <main class="flex-1 relative overflow-y-auto focus:outline-none">
                <div class="py-6">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <%- include(currentPage) %>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Mobile sidebar overlay -->
    <div id="sidebar-overlay" class="fixed inset-0 z-40 lg:hidden hidden">
        <div class="fixed inset-0 bg-gray-600 bg-opacity-75" onclick="toggleSidebar()"></div>
        <div class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
            <!-- Mobile sidebar content would go here -->
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/dashboard.js"></script>
</body>
</html>
