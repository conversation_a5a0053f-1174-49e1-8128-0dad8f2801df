<!-- HAProxy Page -->
<div class="mb-6">
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">HAProxy Statistics</h2>
        <button onclick="refreshHAProxyData()" class="btn-primary">
            <i class="fas fa-sync mr-2"></i>
            Refresh Data
        </button>
    </div>
    <p class="text-gray-600 mt-2">Monitor HAProxy performance and CrowdSec integration</p>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-success-100 rounded-lg">
                        <i class="fas fa-server text-success-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Servers</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="active-servers">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-primary-100 rounded-lg">
                        <i class="fas fa-exchange-alt text-primary-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Requests</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="total-requests-haproxy">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-danger-100 rounded-lg">
                        <i class="fas fa-ban text-danger-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Blocked by CrowdSec</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="crowdsec-blocks">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-warning-100 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-warning-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Error Rate</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="error-rate">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- HAProxy Status -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Server Status -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Server Status</h3>
        </div>
        <div class="p-6">
            <div id="server-status" class="space-y-4">
                <!-- Loading skeleton -->
                <div class="animate-pulse space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="h-4 bg-gray-300 rounded w-1/3"></div>
                        <div class="h-4 bg-gray-300 rounded w-16"></div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="h-4 bg-gray-300 rounded w-1/3"></div>
                        <div class="h-4 bg-gray-300 rounded w-16"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CrowdSec Integration -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">CrowdSec Integration</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4" id="crowdsec-integration">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Lua Plugin Status</span>
                    <span class="status-badge status-loading">
                        <div class="animate-pulse bg-gray-300 h-4 w-16 rounded"></div>
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">API Connection</span>
                    <span class="status-badge status-loading">
                        <div class="animate-pulse bg-gray-300 h-4 w-16 rounded"></div>
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Last Check</span>
                    <span class="timestamp" id="last-check">
                        <div class="animate-pulse bg-gray-300 h-4 w-20 rounded"></div>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- HAProxy Stats Table -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Backend Servers</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="table">
            <thead>
                <tr>
                    <th>Server</th>
                    <th>Status</th>
                    <th>Weight</th>
                    <th>Sessions</th>
                    <th>Bytes In</th>
                    <th>Bytes Out</th>
                    <th>Response Time</th>
                    <th>Health</th>
                </tr>
            </thead>
            <tbody id="haproxy-servers-table">
                <!-- Loading skeleton -->
                <tr>
                    <td colspan="8" class="text-center py-8">
                        <div class="animate-pulse">
                            <div class="h-4 bg-gray-300 rounded w-1/4 mx-auto mb-2"></div>
                            <div class="h-4 bg-gray-300 rounded w-1/2 mx-auto"></div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadHAProxyData();
});

async function loadHAProxyData() {
    try {
        // Load HAProxy stats
        const statsResponse = await fetch('/api/haproxy/stats');
        if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            updateHAProxyStats(statsData.data);
        }

        // Load server status
        const serversResponse = await fetch('/api/haproxy/servers');
        if (serversResponse.ok) {
            const serversData = await serversResponse.json();
            updateServerStatus(serversData.data);
            updateServersTable(serversData.data);
        }

        // Load CrowdSec integration status
        const integrationResponse = await fetch('/api/haproxy/crowdsec-status');
        if (integrationResponse.ok) {
            const integrationData = await integrationResponse.json();
            updateCrowdSecIntegration(integrationData.data);
        }
    } catch (error) {
        console.error('Error loading HAProxy data:', error);
    }
}

function updateHAProxyStats(stats) {
    document.getElementById('active-servers').textContent = stats?.activeServers || 0;
    document.getElementById('total-requests-haproxy').textContent = stats?.totalRequests || 0;
    document.getElementById('crowdsec-blocks').textContent = stats?.crowdsecBlocks || 0;
    document.getElementById('error-rate').textContent = (stats?.errorRate || 0) + '%';
}

function updateServerStatus(servers) {
    const statusContainer = document.getElementById('server-status');
    if (!servers || servers.length === 0) {
        statusContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No server data available</p>';
        return;
    }

    const activeServers = servers.filter(s => s.status === 'UP').length;
    const totalServers = servers.length;

    statusContainer.innerHTML = `
        <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700">Total Servers</span>
            <span class="text-sm text-gray-900">${totalServers}</span>
        </div>
        <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700">Active Servers</span>
            <span class="text-sm text-success-600">${activeServers}</span>
        </div>
        <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700">Inactive Servers</span>
            <span class="text-sm text-danger-600">${totalServers - activeServers}</span>
        </div>
    `;
}

function updateCrowdSecIntegration(integration) {
    const integrationContainer = document.getElementById('crowdsec-integration');
    const lastCheck = document.getElementById('last-check');
    
    // Update integration status
    const luaStatus = integration?.luaPlugin ? 'success' : 'danger';
    const apiStatus = integration?.apiConnection ? 'success' : 'danger';
    
    integrationContainer.innerHTML = `
        <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700">Lua Plugin Status</span>
            <span class="status-badge status-${luaStatus}">
                <i class="fas fa-${luaStatus === 'success' ? 'check-circle' : 'times-circle'} mr-1"></i>
                ${luaStatus === 'success' ? 'Active' : 'Inactive'}
            </span>
        </div>
        <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700">API Connection</span>
            <span class="status-badge status-${apiStatus}">
                <i class="fas fa-${apiStatus === 'success' ? 'check-circle' : 'times-circle'} mr-1"></i>
                ${apiStatus === 'success' ? 'Connected' : 'Disconnected'}
            </span>
        </div>
        <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700">Last Check</span>
            <span class="timestamp">${formatTimestamp(integration?.lastCheck)}</span>
        </div>
    `;
}

function updateServersTable(servers) {
    const tbody = document.getElementById('haproxy-servers-table');
    if (!servers || servers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center py-8 text-gray-500">No server data available</td></tr>';
        return;
    }

    tbody.innerHTML = servers.map(server => `
        <tr class="hover:bg-gray-50">
            <td class="font-medium">${server.name || 'Unknown'}</td>
            <td>
                <span class="status-badge ${server.status === 'UP' ? 'status-success' : 'status-danger'}">
                    <i class="fas fa-${server.status === 'UP' ? 'check-circle' : 'times-circle'} mr-1"></i>
                    ${server.status || 'Unknown'}
                </span>
            </td>
            <td>${server.weight || 0}</td>
            <td>${server.sessions || 0}</td>
            <td>${formatBytes(server.bytesIn || 0)}</td>
            <td>${formatBytes(server.bytesOut || 0)}</td>
            <td>${server.responseTime || 0}ms</td>
            <td>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-${server.health > 80 ? 'success' : server.health > 50 ? 'warning' : 'danger'}-600 h-2 rounded-full" 
                         style="width: ${server.health || 0}%"></div>
                </div>
                <span class="text-xs text-gray-500">${server.health || 0}%</span>
            </td>
        </tr>
    `).join('');
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function refreshHAProxyData() {
    const refreshBtn = document.querySelector('[onclick="refreshHAProxyData()"]');
    const icon = refreshBtn.querySelector('i');
    
    // Add spinning animation
    icon.classList.add('fa-spin');
    
    // Reload all data
    loadHAProxyData();
    
    // Remove spinning animation after 1 second
    setTimeout(() => {
        icon.classList.remove('fa-spin');
    }, 1000);
}
</script>
