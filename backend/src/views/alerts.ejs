<!-- Alerts Page -->
<div class="mb-6">
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">Security Alerts</h2>
        <button onclick="syncAlerts()" class="btn-primary">
            <i class="fas fa-sync mr-2"></i>
            Sync Alerts
        </button>
    </div>
    <p class="text-gray-600 mt-2">Monitor and analyze security threats detected by CrowdSec</p>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-danger-100 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-danger-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Alerts</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="total-alerts">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-warning-100 rounded-lg">
                        <i class="fas fa-clock text-warning-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Last 24h</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="alerts-24h">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-primary-100 rounded-lg">
                        <i class="fas fa-shield-alt text-primary-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Unique IPs</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="unique-ips">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-card bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="p-3 bg-success-100 rounded-lg">
                        <i class="fas fa-check-circle text-success-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Resolved</dt>
                        <dd class="text-2xl font-semibold text-gray-900" id="resolved-alerts">
                            <div class="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Filters</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Severity</label>
                <select id="severity-filter" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="">All Severities</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Scenario</label>
                <select id="scenario-filter" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="">All Scenarios</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Source IP</label>
                <input type="text" id="ip-filter" placeholder="Enter IP address" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500">
            </div>
            <div class="flex items-end">
                <button onclick="applyFilters()" class="btn-primary w-full">
                    <i class="fas fa-filter mr-2"></i>
                    Apply Filters
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Alerts Table -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Recent Alerts</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="table">
            <thead>
                <tr>
                    <th>Severity</th>
                    <th>Scenario</th>
                    <th>Source IP</th>
                    <th>Country</th>
                    <th>Events</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="alerts-table-body">
                <!-- Loading skeleton -->
                <tr>
                    <td colspan="7" class="text-center py-8">
                        <div class="animate-pulse">
                            <div class="h-4 bg-gray-300 rounded w-1/4 mx-auto mb-2"></div>
                            <div class="h-4 bg-gray-300 rounded w-1/2 mx-auto"></div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing <span id="showing-from">1</span> to <span id="showing-to">10</span> of <span id="total-count">0</span> alerts
            </div>
            <div class="flex space-x-2">
                <button onclick="previousPage()" id="prev-btn" class="btn-secondary" disabled>
                    <i class="fas fa-chevron-left mr-1"></i>
                    Previous
                </button>
                <button onclick="nextPage()" id="next-btn" class="btn-secondary">
                    Next
                    <i class="fas fa-chevron-right ml-1"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Alerts page specific JavaScript
let currentPage = 1;
let currentFilters = {};

document.addEventListener('DOMContentLoaded', function() {
    loadAlertsData();
    loadScenarios();
});

async function loadAlertsData() {
    try {
        // Load stats
        const statsResponse = await fetch('/api/alerts/stats');
        if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            updateAlertsStats(statsData.data);
        }

        // Load alerts table
        const alertsResponse = await fetch(`/api/alerts?page=${currentPage}&limit=10`);
        if (alertsResponse.ok) {
            const alertsData = await alertsResponse.json();
            updateAlertsTable(alertsData.data);
            updatePagination(alertsData.pagination);
        }
    } catch (error) {
        console.error('Error loading alerts data:', error);
    }
}

function updateAlertsStats(stats) {
    document.getElementById('total-alerts').textContent = stats?.total || 0;
    document.getElementById('alerts-24h').textContent = stats?.last24h || 0;
    document.getElementById('unique-ips').textContent = stats?.uniqueIps || 0;
    document.getElementById('resolved-alerts').textContent = stats?.resolved || 0;
}

function updateAlertsTable(alerts) {
    const tbody = document.getElementById('alerts-table-body');
    if (!alerts || alerts.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center py-8 text-gray-500">No alerts found</td></tr>';
        return;
    }

    tbody.innerHTML = alerts.map(alert => `
        <tr class="hover:bg-gray-50">
            <td>
                <span class="status-badge severity-${alert.severity || 'info'}">
                    ${alert.severity || 'info'}
                </span>
            </td>
            <td class="font-medium">${alert.scenario || 'Unknown'}</td>
            <td><span class="ip-address">${alert.source_ip || 'Unknown'}</span></td>
            <td>${alert.country || 'Unknown'}</td>
            <td>${alert.events_count || 0}</td>
            <td class="timestamp">${formatTimestamp(alert.created_at)}</td>
            <td>
                <button onclick="viewAlert('${alert.id}')" class="text-primary-600 hover:text-primary-500 text-sm">
                    <i class="fas fa-eye mr-1"></i>
                    View
                </button>
            </td>
        </tr>
    `).join('');
}

function updatePagination(pagination) {
    if (pagination) {
        document.getElementById('showing-from').textContent = pagination.offset + 1;
        document.getElementById('showing-to').textContent = Math.min(pagination.offset + pagination.limit, pagination.total);
        document.getElementById('total-count').textContent = pagination.total;
        
        document.getElementById('prev-btn').disabled = pagination.page <= 1;
        document.getElementById('next-btn').disabled = pagination.page >= pagination.totalPages;
    }
}

async function loadScenarios() {
    try {
        const response = await fetch('/api/alerts?scenarios=true');
        if (response.ok) {
            const data = await response.json();
            const scenarios = data.scenarios || [];
            const select = document.getElementById('scenario-filter');
            scenarios.forEach(scenario => {
                const option = document.createElement('option');
                option.value = scenario;
                option.textContent = scenario;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading scenarios:', error);
    }
}

function applyFilters() {
    currentFilters = {
        severity: document.getElementById('severity-filter').value,
        scenario: document.getElementById('scenario-filter').value,
        source_ip: document.getElementById('ip-filter').value
    };
    currentPage = 1;
    loadAlertsData();
}

function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        loadAlertsData();
    }
}

function nextPage() {
    currentPage++;
    loadAlertsData();
}

function viewAlert(alertId) {
    // Mock alert detail view
    showNotification('Alert detail view coming soon', 'info');
}

async function syncAlerts() {
    try {
        const response = await fetch('/api/alerts/sync', { method: 'POST' });
        if (response.ok) {
            showNotification('Alerts synced successfully', 'success');
            loadAlertsData();
        } else {
            showNotification('Failed to sync alerts', 'error');
        }
    } catch (error) {
        showNotification('Error syncing alerts', 'error');
    }
}
</script>
