import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config();

export interface Config {
  app: {
    name: string;
    version: string;
    port: number;
    env: string;
    logLevel: string;
  };
  database: {
    type: 'postgresql' | 'sqlite';
    postgresql?: {
      host: string;
      port: number;
      database: string;
      username: string;
      password: string;
      ssl: boolean;
    };
    sqlite?: {
      filename: string;
    };
  };
  redis: {
    url: string;
    host: string;
    port: number;
    password?: string;
  };
  crowdsec: {
    apiUrl: string;
    apiKey?: string;
    dbPath: string;
    configPath: string;
  };
  haproxy: {
    statsUrl: string;
    statsAuth?: {
      username: string;
      password: string;
    };
    logPath?: string;
  };
  security: {
    jwtSecret: string;
    sessionSecret: string;
    corsOrigin: string;
    rateLimitMax: number;
  };
  monitoring: {
    enableMetrics: boolean;
    metricsPort: number;
  };
  retention: {
    alertDays: number;
    decisionDays: number;
    metricsDays: number;
  };
}

const config: Config = {
  app: {
    name: 'CrowdSec Dashboard API',
    version: '1.0.0',
    port: parseInt(process.env.PORT || '3001', 10),
    env: process.env.NODE_ENV || 'development',
    logLevel: process.env.LOG_LEVEL || 'info',
  },
  database: {
    type: process.env.DATABASE_TYPE as 'postgresql' | 'sqlite' || 'sqlite',
    postgresql: process.env.DATABASE_URL ? {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432', 10),
      database: process.env.DB_NAME || 'crowdsec_dashboard',
      username: process.env.DB_USER || 'crowdsec_user',
      password: process.env.DB_PASSWORD || 'crowdsec_password',
      ssl: process.env.DB_SSL === 'true',
    } : undefined,
    sqlite: {
      filename: process.env.SQLITE_DB_PATH || path.join(process.cwd(), 'data', 'dashboard.db'),
    },
  },
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD,
  },
  crowdsec: {
    apiUrl: process.env.CROWDSEC_API_URL || 'http://127.0.0.1:8080',
    apiKey: process.env.CROWDSEC_API_KEY,
    dbPath: process.env.CROWDSEC_DB_PATH || '/var/lib/crowdsec/data/crowdsec.db',
    configPath: process.env.CROWDSEC_CONFIG_PATH || '/etc/crowdsec',
  },
  haproxy: {
    statsUrl: process.env.HAPROXY_STATS_URL || 'http://localhost:8404/stats',
    statsAuth: process.env.HAPROXY_STATS_AUTH ? {
      username: process.env.HAPROXY_STATS_AUTH.split(':')[0],
      password: process.env.HAPROXY_STATS_AUTH.split(':')[1],
    } : undefined,
    logPath: process.env.HAPROXY_LOG_PATH || '/var/log/haproxy.log',
  },
  security: {
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    sessionSecret: process.env.SESSION_SECRET || 'your-super-secret-session-key-change-in-production',
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    rateLimitMax: parseInt(process.env.API_RATE_LIMIT || '100', 10),
  },
  monitoring: {
    enableMetrics: process.env.ENABLE_METRICS === 'true',
    metricsPort: parseInt(process.env.METRICS_PORT || '9090', 10),
  },
  retention: {
    alertDays: parseInt(process.env.ALERT_RETENTION_DAYS || '30', 10),
    decisionDays: parseInt(process.env.DECISION_RETENTION_DAYS || '90', 10),
    metricsDays: parseInt(process.env.METRICS_RETENTION_DAYS || '365', 10),
  },
};

// Validation
if (!config.security.jwtSecret || config.security.jwtSecret.includes('change-in-production')) {
  if (config.app.env === 'production') {
    throw new Error('JWT_SECRET must be set in production environment');
  }
  console.warn('⚠️  Using default JWT secret. Change JWT_SECRET in production!');
}

if (config.database.type === 'postgresql' && !config.database.postgresql) {
  throw new Error('PostgreSQL configuration is required when DATABASE_TYPE is postgresql');
}

export default config;
