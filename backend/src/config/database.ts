import { Pool } from 'pg';
import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs';

export interface DatabaseConfig {
  type: 'postgresql' | 'sqlite';
  postgresql?: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl?: boolean;
  };
  sqlite?: {
    filename: string;
  };
}

export class Database {
  private pgPool?: Pool;
  private sqliteDb?: sqlite3.Database;
  private config: DatabaseConfig;

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  async connect(): Promise<void> {
    if (this.config.type === 'postgresql' && this.config.postgresql) {
      this.pgPool = new Pool({
        host: this.config.postgresql.host,
        port: this.config.postgresql.port,
        database: this.config.postgresql.database,
        user: this.config.postgresql.username,
        password: this.config.postgresql.password,
        ssl: this.config.postgresql.ssl,
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });

      // Test connection
      const client = await this.pgPool.connect();
      client.release();
      console.log('✅ PostgreSQL connected successfully');
    } else if (this.config.type === 'sqlite' && this.config.sqlite) {
      const dbPath = path.resolve(this.config.sqlite.filename);
      const dbDir = path.dirname(dbPath);
      
      // Ensure directory exists
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      this.sqliteDb = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          console.error('❌ SQLite connection error:', err);
          throw err;
        }
        console.log('✅ SQLite connected successfully');
      });

      // Enable foreign keys
      await this.run('PRAGMA foreign_keys = ON');
    }
  }

  async disconnect(): Promise<void> {
    if (this.pgPool) {
      await this.pgPool.end();
      console.log('PostgreSQL disconnected');
    }
    if (this.sqliteDb) {
      await new Promise<void>((resolve, reject) => {
        this.sqliteDb!.close((err) => {
          if (err) reject(err);
          else {
            console.log('SQLite disconnected');
            resolve();
          }
        });
      });
    }
  }

  async query(sql: string, params: any[] = []): Promise<any[]> {
    if (this.pgPool) {
      const result = await this.pgPool.query(sql, params);
      return result.rows;
    } else if (this.sqliteDb) {
      const all = promisify(this.sqliteDb.all.bind(this.sqliteDb));
      return await all(sql) as any[];
    }
    throw new Error('No database connection available');
  }

  async run(sql: string, params: any[] = []): Promise<any> {
    if (this.pgPool) {
      const result = await this.pgPool.query(sql, params);
      return result;
    } else if (this.sqliteDb) {
      const run = promisify(this.sqliteDb.run.bind(this.sqliteDb));
      return await run(sql);
    }
    throw new Error('No database connection available');
  }

  async get(sql: string, params: any[] = []): Promise<any> {
    if (this.pgPool) {
      const result = await this.pgPool.query(sql, params);
      return result.rows[0];
    } else if (this.sqliteDb) {
      const get = promisify(this.sqliteDb.get.bind(this.sqliteDb));
      return await get(sql);
    }
    throw new Error('No database connection available');
  }

  async transaction<T>(callback: (db: Database) => Promise<T>): Promise<T> {
    if (this.pgPool) {
      const client = await this.pgPool.connect();
      try {
        await client.query('BEGIN');
        const transactionDb = new Database(this.config);
        transactionDb.pgPool = { query: client.query.bind(client) } as any;
        const result = await callback(transactionDb);
        await client.query('COMMIT');
        return result;
      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }
    } else if (this.sqliteDb) {
      await this.run('BEGIN TRANSACTION');
      try {
        const result = await callback(this);
        await this.run('COMMIT');
        return result;
      } catch (error) {
        await this.run('ROLLBACK');
        throw error;
      }
    }
    throw new Error('No database connection available');
  }

  getType(): 'postgresql' | 'sqlite' {
    return this.config.type;
  }
}

// Database instance
let db: Database;

export function initializeDatabase(config: DatabaseConfig): Database {
  db = new Database(config);
  return db;
}

export function getDatabase(): Database {
  if (!db) {
    throw new Error('Database not initialized. Call initializeDatabase first.');
  }
  return db;
}
