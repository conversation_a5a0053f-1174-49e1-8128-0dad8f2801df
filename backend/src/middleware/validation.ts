import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import logger from '../utils/logger';

export const validateQuery = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    logger.warn('Query validation failed:', {
      url: req.url,
      method: req.method,
      errors: errors.array(),
      query: req.query,
    });
    
    res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
    return;
  }
  
  next();
};

export const validateParams = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    logger.warn('Parameter validation failed:', {
      url: req.url,
      method: req.method,
      errors: errors.array(),
      params: req.params,
    });
    
    res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
    return;
  }
  
  next();
};

export const validateBody = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    logger.warn('Body validation failed:', {
      url: req.url,
      method: req.method,
      errors: errors.array(),
      body: req.body,
    });
    
    res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
    return;
  }
  
  next();
};
