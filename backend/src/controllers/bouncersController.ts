import { Request, Response } from 'express';
import { getDatabase } from '../config/database';
import CrowdSecService from '../services/crowdsecService';
import logger from '../utils/logger';

export class BouncersController {
  private db: any;
  private crowdsecService: any;

  constructor() {
    this.db = getDatabase();
    this.crowdsecService = new CrowdSecService();
  }

  // GET /api/bouncers - Alle Bouncers abrufen
  async getBouncers(req: Request, res: Response): Promise<void> {
    try {
      const bouncers = await this.db.query(`
        SELECT 
          b.*,
          CASE 
            WHEN b.last_pull >= ${
              this.db.getType() === 'sqlite' 
                ? "datetime('now', '-5 minutes')" 
                : "NOW() - INTERVAL '5 minutes'"
            } THEN 'online'
            WHEN b.last_pull >= ${
              this.db.getType() === 'sqlite'
                ? "datetime('now', '-15 minutes')"
                : "NOW() - INTERVAL '15 minutes'"
            } THEN 'warning'
            ELSE 'offline'
          END as status
        FROM bouncers b
        WHERE b.is_active = 1
        ORDER BY b.name
      `);

      res.json({
        success: true,
        data: bouncers,
      });
    } catch (error) {
      logger.error('Error fetching bouncers:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch bouncers',
      });
    }
  }

  // GET /api/bouncers/:id - Einzelnen Bouncer abrufen
  async getBouncer(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const bouncer = await this.db.get(`
        SELECT 
          b.*,
          CASE 
            WHEN b.last_pull >= ${
              this.db.getType() === 'sqlite'
                ? "datetime('now', '-5 minutes')"
                : "NOW() - INTERVAL '5 minutes'"
            } THEN 'online'
            WHEN b.last_pull >= ${
              this.db.getType() === 'sqlite'
                ? "datetime('now', '-15 minutes')"
                : "NOW() - INTERVAL '15 minutes'"
            } THEN 'warning'
            ELSE 'offline'
          END as status
        FROM bouncers b
        WHERE b.id = ? OR b.name = ?
      `, [id, id]);

      if (!bouncer) {
        res.status(404).json({
          success: false,
          error: 'Bouncer not found',
        });
        return;
      }

      res.json({
        success: true,
        data: bouncer,
      });
    } catch (error) {
      logger.error('Error fetching bouncer:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch bouncer',
      });
    }
  }

  // GET /api/bouncers/:id/metrics - Bouncer Metriken
  async getBouncerMetrics(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { period = '24h' } = req.query;

      // Get bouncer first
      const bouncer = await this.db.get(
        'SELECT * FROM bouncers WHERE id = ? OR name = ?',
        [id, id]
      );

      if (!bouncer) {
        res.status(404).json({
          success: false,
          error: 'Bouncer not found',
        });
        return;
      }

      let timeFilter = '';
      switch (period) {
        case '1h':
          timeFilter = this.db.getType() === 'sqlite'
            ? "recorded_at >= datetime('now', '-1 hour')"
            : "recorded_at >= NOW() - INTERVAL '1 hour'";
          break;
        case '24h':
          timeFilter = this.db.getType() === 'sqlite'
            ? "recorded_at >= datetime('now', '-24 hours')"
            : "recorded_at >= NOW() - INTERVAL '24 hours'";
          break;
        case '7d':
          timeFilter = this.db.getType() === 'sqlite'
            ? "recorded_at >= datetime('now', '-7 days')"
            : "recorded_at >= NOW() - INTERVAL '7 days'";
          break;
        default:
          timeFilter = this.db.getType() === 'sqlite'
            ? "recorded_at >= datetime('now', '-24 hours')"
            : "recorded_at >= NOW() - INTERVAL '24 hours'";
      }

      const metrics = await this.db.query(`
        SELECT * FROM bouncer_metrics 
        WHERE bouncer_id = ? AND ${timeFilter}
        ORDER BY recorded_at DESC
      `, [bouncer.id]);

      // Calculate aggregated stats
      const stats = await this.db.get(`
        SELECT 
          SUM(requests_total) as total_requests,
          SUM(requests_blocked) as total_blocked,
          SUM(requests_allowed) as total_allowed,
          AVG(response_time_avg) as avg_response_time,
          AVG(cpu_usage) as avg_cpu_usage,
          AVG(memory_usage) as avg_memory_usage
        FROM bouncer_metrics 
        WHERE bouncer_id = ? AND ${timeFilter}
      `, [bouncer.id]);

      res.json({
        success: true,
        data: {
          bouncer,
          metrics,
          stats: stats || {
            total_requests: 0,
            total_blocked: 0,
            total_allowed: 0,
            avg_response_time: 0,
            avg_cpu_usage: 0,
            avg_memory_usage: 0,
          },
        },
      });
    } catch (error) {
      logger.error('Error fetching bouncer metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch bouncer metrics',
      });
    }
  }

  // POST /api/bouncers/sync - Synchronisation mit CrowdSec
  async syncBouncers(req: Request, res: Response): Promise<void> {
    try {
      await this.crowdsecService.syncBouncersToDatabase();
      
      res.json({
        success: true,
        message: 'Bouncers synchronized successfully',
      });
    } catch (error) {
      logger.error('Error syncing bouncers:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to sync bouncers',
      });
    }
  }

  // GET /api/bouncers/stats - Bouncer Statistiken
  async getBouncerStats(req: Request, res: Response): Promise<void> {
    try {
      const [
        totalBouncers,
        onlineBouncers,
        offlineBouncers,
        recentMetrics,
      ] = await Promise.all([
        // Total bouncers
        this.db.get('SELECT COUNT(*) as count FROM bouncers WHERE is_active = 1'),
        
        // Online bouncers
        this.db.get(`
          SELECT COUNT(*) as count FROM bouncers 
          WHERE is_active = 1 
          AND last_pull >= ${
            this.db.getType() === 'sqlite'
              ? "datetime('now', '-5 minutes')"
              : "NOW() - INTERVAL '5 minutes'"
          }
        `),
        
        // Offline bouncers
        this.db.get(`
          SELECT COUNT(*) as count FROM bouncers 
          WHERE is_active = 1 
          AND last_pull < ${
            this.db.getType() === 'sqlite'
              ? "datetime('now', '-15 minutes')"
              : "NOW() - INTERVAL '15 minutes'"
          }
        `),
        
        // Recent metrics summary
        this.db.get(`
          SELECT 
            SUM(requests_total) as total_requests,
            SUM(requests_blocked) as total_blocked,
            SUM(requests_allowed) as total_allowed,
            AVG(response_time_avg) as avg_response_time
          FROM bouncer_metrics 
          WHERE recorded_at >= ${
            this.db.getType() === 'sqlite'
              ? "datetime('now', '-1 hour')"
              : "NOW() - INTERVAL '1 hour'"
          }
        `),
      ]);

      const warningBouncers = (totalBouncers?.count || 0) - 
                             (onlineBouncers?.count || 0) - 
                             (offlineBouncers?.count || 0);

      res.json({
        success: true,
        data: {
          total: totalBouncers?.count || 0,
          online: onlineBouncers?.count || 0,
          warning: Math.max(0, warningBouncers),
          offline: offlineBouncers?.count || 0,
          metrics: recentMetrics || {
            total_requests: 0,
            total_blocked: 0,
            total_allowed: 0,
            avg_response_time: 0,
          },
        },
      });
    } catch (error) {
      logger.error('Error fetching bouncer stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch bouncer statistics',
      });
    }
  }
}

export default new BouncersController();
