import { Request, Response } from 'express';
import HAProxyService from '../services/haproxyService';
import logger from '../utils/logger';

export class HAProxyController {
  private haproxyService: any;

  constructor() {
    this.haproxyService = new HAProxyService();
  }
  // GET /api/haproxy/stats - HAProxy Statistiken abrufen
  async getStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.haproxyService.getHAProxyStats();
      
      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      logger.error('Error fetching HAProxy stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch HAProxy statistics',
      });
    }
  }

  // GET /api/haproxy/metrics - Aggregierte HAProxy Metriken
  async getMetrics(req: Request, res: Response): Promise<void> {
    try {
      const { period = '24h' } = req.query;
      
      const metrics = await this.haproxyService.getAggregatedStats(period as string);
      
      res.json({
        success: true,
        data: metrics,
      });
    } catch (error) {
      logger.error('Error fetching HAProxy metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch HAProxy metrics',
      });
    }
  }

  // GET /api/haproxy/crowdsec - CrowdSec Lua Plugin Statistiken
  async getCrowdSecStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.haproxyService.getCrowdSecLuaStats();
      
      if (!stats) {
        res.status(404).json({
          success: false,
          error: 'CrowdSec Lua statistics not available',
        });
        return;
      }

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      logger.error('Error fetching CrowdSec Lua stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch CrowdSec Lua statistics',
      });
    }
  }

  // GET /api/haproxy/health - HAProxy Health Check
  async getHealth(req: Request, res: Response): Promise<void> {
    try {
      const health = await this.haproxyService.healthCheck();
      
      const statusCode = health.status === 'healthy' ? 200 : 503;
      
      res.status(statusCode).json({
        success: health.status === 'healthy',
        data: health,
      });
    } catch (error) {
      logger.error('Error checking HAProxy health:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check HAProxy health',
      });
    }
  }

  // POST /api/haproxy/sync - HAProxy Daten synchronisieren
  async syncData(req: Request, res: Response): Promise<void> {
    try {
      await this.haproxyService.syncAllData();
      
      res.json({
        success: true,
        message: 'HAProxy data synchronized successfully',
      });
    } catch (error) {
      logger.error('Error syncing HAProxy data:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to sync HAProxy data',
      });
    }
  }

  // GET /api/haproxy/frontends - Frontend Statistiken
  async getFrontends(req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.haproxyService.getHAProxyStats();
      
      // Filtere nur Frontend Statistiken (type = 0)
      const frontends = stats.filter((stat: any) => stat.type === 0);
      
      res.json({
        success: true,
        data: frontends,
      });
    } catch (error) {
      logger.error('Error fetching HAProxy frontends:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch HAProxy frontends',
      });
    }
  }

  // GET /api/haproxy/backends - Backend Statistiken
  async getBackends(req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.haproxyService.getHAProxyStats();
      
      // Filtere nur Backend Statistiken (type = 1)
      const backends = stats.filter((stat: any) => stat.type === 1);
      
      res.json({
        success: true,
        data: backends,
      });
    } catch (error) {
      logger.error('Error fetching HAProxy backends:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch HAProxy backends',
      });
    }
  }

  // GET /api/haproxy/servers - Server Statistiken
  async getServers(req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.haproxyService.getHAProxyStats();
      
      // Filtere nur Server Statistiken (type = 2)
      const servers = stats.filter((stat: any) => stat.type === 2);
      
      res.json({
        success: true,
        data: servers,
      });
    } catch (error) {
      logger.error('Error fetching HAProxy servers:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch HAProxy servers',
      });
    }
  }

  // GET /api/haproxy/dashboard - Dashboard Übersicht
  async getDashboardData(req: Request, res: Response): Promise<void> {
    try {
      const [
        stats,
        metrics,
        crowdsecStats,
        health,
      ] = await Promise.all([
        this.haproxyService.getHAProxyStats(),
        this.haproxyService.getAggregatedStats('1h'),
        this.haproxyService.getCrowdSecLuaStats(),
        this.haproxyService.healthCheck(),
      ]);

      // Berechne Zusammenfassung
      const frontends = stats.filter((s: any) => s.type === 0);
      const backends = stats.filter((s: any) => s.type === 1);
      const servers = stats.filter((s: any) => s.type === 2);

      const activeFrontends = frontends.filter((f: any) => f.status === 'OPEN').length;
      const activeBackends = backends.filter((b: any) => b.status === 'UP').length;
      const activeServers = servers.filter((s: any) => s.status === 'UP').length;

      const totalSessions = frontends.reduce((sum: any, f: any) => sum + (f.scur || 0), 0);
      const totalRequests = frontends.reduce((sum: any, f: any) => sum + (f.stot || 0), 0);

      res.json({
        success: true,
        data: {
          summary: {
            frontends: {
              total: frontends.length,
              active: activeFrontends,
            },
            backends: {
              total: backends.length,
              active: activeBackends,
            },
            servers: {
              total: servers.length,
              active: activeServers,
            },
            sessions: {
              current: totalSessions,
              total: totalRequests,
            },
          },
          health: health,
          crowdsec: crowdsecStats,
          metrics: metrics,
          recent_stats: {
            frontends: frontends.slice(0, 5),
            backends: backends.slice(0, 5),
          },
        },
      });
    } catch (error) {
      logger.error('Error fetching HAProxy dashboard data:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch HAProxy dashboard data',
      });
    }
  }
}

export default new HAProxyController();
