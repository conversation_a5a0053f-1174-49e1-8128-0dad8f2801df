import { Request, Response } from 'express';
import { getDatabase } from '../config/database';
import CrowdSecService from '../services/crowdsecService';
import logger from '../utils/logger';

export class DecisionsController {
  private db: any;
  private crowdsecService: any;

  constructor() {
    this.db = getDatabase();
    this.crowdsecService = new CrowdSecService();
  }

  // GET /api/decisions - Alle Decisions abrufen
  async getDecisions(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 50,
        type,
        scope,
        value,
        scenario,
        active_only = 'true',
      } = req.query;

      const offset = (Number(page) - 1) * Number(limit);
      let whereClause = '1=1';
      const params: any[] = [];
      let paramIndex = 1;

      // Filter conditions
      if (type) {
        whereClause += ` AND type = $${paramIndex++}`;
        params.push(type);
      }
      if (scope) {
        whereClause += ` AND scope = $${paramIndex++}`;
        params.push(scope);
      }
      if (value) {
        whereClause += ` AND value LIKE $${paramIndex++}`;
        params.push(`%${value}%`);
      }
      if (scenario) {
        whereClause += ` AND scenario LIKE $${paramIndex++}`;
        params.push(`%${scenario}%`);
      }
      if (active_only === 'true') {
        whereClause += ` AND deleted_at IS NULL AND (until IS NULL OR until > ${
          this.db.getType() === 'sqlite' ? "datetime('now')" : 'NOW()'
        })`;
      }

      // Adjust for SQLite
      if (this.db.getType() === 'sqlite') {
        whereClause = whereClause.replace(/\$\d+/g, '?');
      }

      // Get decisions with pagination
      const decisionsQuery = `
        SELECT * FROM decisions 
        WHERE ${whereClause}
        ORDER BY created_at DESC
        LIMIT ${Number(limit)} OFFSET ${offset}
      `;

      const countQuery = `
        SELECT COUNT(*) as total FROM decisions 
        WHERE ${whereClause}
      `;

      const [decisions, countResult] = await Promise.all([
        this.db.query(decisionsQuery, params),
        this.db.get(countQuery, params),
      ]);

      const total = countResult?.total || 0;
      const totalPages = Math.ceil(total / Number(limit));

      res.json({
        success: true,
        data: decisions,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages,
          hasNext: Number(page) < totalPages,
          hasPrev: Number(page) > 1,
        },
      });
    } catch (error) {
      logger.error('Error fetching decisions:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch decisions',
      });
    }
  }

  // GET /api/decisions/:id - Einzelne Decision abrufen
  async getDecision(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const decision = await this.db.get(
        'SELECT * FROM decisions WHERE id = ? OR crowdsec_id = ?',
        [id, id]
      );

      if (!decision) {
        res.status(404).json({
          success: false,
          error: 'Decision not found',
        });
        return;
      }

      res.json({
        success: true,
        data: decision,
      });
    } catch (error) {
      logger.error('Error fetching decision:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch decision',
      });
    }
  }

  // POST /api/decisions - Neue Decision erstellen
  async createDecision(req: Request, res: Response): Promise<void> {
    try {
      const { type, scope, value, duration, scenario } = req.body;

      // Validate required fields
      if (!type || !scope || !value) {
        res.status(400).json({
          success: false,
          error: 'Type, scope, and value are required',
        });
        return;
      }

      // Create decision via CrowdSec API
      const success = await this.crowdsecService.addDecision({
        type,
        scope,
        value,
        duration: duration || '4h',
        scenario: scenario || 'manual',
        origin: 'dashboard',
        simulated: false,
      });

      if (!success) {
        res.status(500).json({
          success: false,
          error: 'Failed to create decision in CrowdSec',
        });
        return;
      }

      // Sync decisions to get the new one
      await this.crowdsecService.syncDecisionsToDatabase();

      res.json({
        success: true,
        message: 'Decision created successfully',
      });
    } catch (error) {
      logger.error('Error creating decision:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create decision',
      });
    }
  }

  // DELETE /api/decisions/:id - Decision löschen
  async deleteDecision(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Get decision first
      const decision = await this.db.get(
        'SELECT * FROM decisions WHERE id = ? OR crowdsec_id = ?',
        [id, id]
      );

      if (!decision) {
        res.status(404).json({
          success: false,
          error: 'Decision not found',
        });
        return;
      }

      // Delete via CrowdSec API
      const success = await this.crowdsecService.deleteDecision(decision.crowdsec_id);

      if (!success) {
        res.status(500).json({
          success: false,
          error: 'Failed to delete decision in CrowdSec',
        });
        return;
      }

      // Mark as deleted in database
      await this.db.run(
        'UPDATE decisions SET deleted_at = CURRENT_TIMESTAMP WHERE id = ?',
        [decision.id]
      );

      res.json({
        success: true,
        message: 'Decision deleted successfully',
      });
    } catch (error) {
      logger.error('Error deleting decision:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete decision',
      });
    }
  }

  // GET /api/decisions/stats - Decision Statistiken
  async getDecisionStats(req: Request, res: Response): Promise<void> {
    try {
      const { period = '24h' } = req.query;
      
      let timeFilter = '';
      switch (period) {
        case '1h':
          timeFilter = this.db.getType() === 'sqlite' 
            ? "created_at >= datetime('now', '-1 hour')"
            : "created_at >= NOW() - INTERVAL '1 hour'";
          break;
        case '24h':
          timeFilter = this.db.getType() === 'sqlite'
            ? "created_at >= datetime('now', '-24 hours')"
            : "created_at >= NOW() - INTERVAL '24 hours'";
          break;
        case '7d':
          timeFilter = this.db.getType() === 'sqlite'
            ? "created_at >= datetime('now', '-7 days')"
            : "created_at >= NOW() - INTERVAL '7 days'";
          break;
        case '30d':
          timeFilter = this.db.getType() === 'sqlite'
            ? "created_at >= datetime('now', '-30 days')"
            : "created_at >= NOW() - INTERVAL '30 days'";
          break;
        default:
          timeFilter = this.db.getType() === 'sqlite'
            ? "created_at >= datetime('now', '-24 hours')"
            : "created_at >= NOW() - INTERVAL '24 hours'";
      }

      const [
        totalDecisions,
        activeDecisions,
        decisionsByType,
        decisionsByScope,
        topValues,
      ] = await Promise.all([
        // Total decisions
        this.db.get(`SELECT COUNT(*) as count FROM decisions WHERE ${timeFilter}`),
        
        // Active decisions
        this.db.get(`
          SELECT COUNT(*) as count FROM decisions 
          WHERE deleted_at IS NULL 
          AND (until IS NULL OR until > ${
            this.db.getType() === 'sqlite' ? "datetime('now')" : 'NOW()'
          })
        `),
        
        // Decisions by type
        this.db.query(`
          SELECT type, COUNT(*) as count 
          FROM decisions 
          WHERE ${timeFilter}
          GROUP BY type
        `),
        
        // Decisions by scope
        this.db.query(`
          SELECT scope, COUNT(*) as count 
          FROM decisions 
          WHERE ${timeFilter}
          GROUP BY scope
        `),
        
        // Top blocked values
        this.db.query(`
          SELECT value, COUNT(*) as count 
          FROM decisions 
          WHERE ${timeFilter} AND type = 'ban'
          GROUP BY value 
          ORDER BY count DESC 
          LIMIT 10
        `),
      ]);

      res.json({
        success: true,
        data: {
          total: totalDecisions?.count || 0,
          active: activeDecisions?.count || 0,
          by_type: decisionsByType,
          by_scope: decisionsByScope,
          top_blocked: topValues,
        },
      });
    } catch (error) {
      logger.error('Error fetching decision stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch decision statistics',
      });
    }
  }

  // POST /api/decisions/sync - Synchronisation mit CrowdSec
  async syncDecisions(req: Request, res: Response): Promise<void> {
    try {
      await this.crowdsecService.syncDecisionsToDatabase();
      
      res.json({
        success: true,
        message: 'Decisions synchronized successfully',
      });
    } catch (error) {
      logger.error('Error syncing decisions:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to sync decisions',
      });
    }
  }
}

export default new DecisionsController();
