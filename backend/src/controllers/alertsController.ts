import { Request, Response } from 'express';
import { getDatabase } from '../config/database';
import CrowdSecService from '../services/crowdsecService';
import logger from '../utils/logger';

export class AlertsController {
  private db: any;
  private crowdsecService: any;

  constructor() {
    this.db = getDatabase();
    this.crowdsecService = new CrowdSecService();
  }

  // GET /api/alerts - Alle Alerts abrufen
  async getAlerts(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 50,
        status,
        severity,
        scenario,
        source_ip,
        start_date,
        end_date,
      } = req.query;

      const offset = (Number(page) - 1) * Number(limit);
      let whereClause = '1=1';
      const params: any[] = [];
      let paramIndex = 1;

      // Filter conditions
      if (status) {
        whereClause += ` AND status = $${paramIndex++}`;
        params.push(status);
      }
      if (severity) {
        whereClause += ` AND severity = $${paramIndex++}`;
        params.push(severity);
      }
      if (scenario) {
        whereClause += ` AND scenario LIKE $${paramIndex++}`;
        params.push(`%${scenario}%`);
      }
      if (source_ip) {
        whereClause += ` AND source_ip = $${paramIndex++}`;
        params.push(source_ip);
      }
      if (start_date) {
        whereClause += ` AND start_at >= $${paramIndex++}`;
        params.push(start_date);
      }
      if (end_date) {
        whereClause += ` AND start_at <= $${paramIndex++}`;
        params.push(end_date);
      }

      // Adjust for SQLite
      if (this.db.getType() === 'sqlite') {
        whereClause = whereClause.replace(/\$\d+/g, '?');
      }

      // Get alerts with pagination
      const alertsQuery = `
        SELECT * FROM alerts 
        WHERE ${whereClause}
        ORDER BY start_at DESC
        LIMIT ${Number(limit)} OFFSET ${offset}
      `;

      const countQuery = `
        SELECT COUNT(*) as total FROM alerts 
        WHERE ${whereClause}
      `;

      const [alerts, countResult] = await Promise.all([
        this.db.query(alertsQuery, params),
        this.db.get(countQuery, params),
      ]);

      const total = countResult?.total || 0;
      const totalPages = Math.ceil(total / Number(limit));

      res.json({
        success: true,
        data: alerts,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages,
          hasNext: Number(page) < totalPages,
          hasPrev: Number(page) > 1,
        },
      });
    } catch (error) {
      logger.error('Error fetching alerts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch alerts',
      });
    }
  }

  // GET /api/alerts/:id - Einzelnen Alert abrufen
  async getAlert(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const alert = await this.db.get(
        'SELECT * FROM alerts WHERE id = ? OR crowdsec_id = ?',
        [id, id]
      );

      if (!alert) {
        res.status(404).json({
          success: false,
          error: 'Alert not found',
        });
        return;
      }

      // Get related decisions
      const decisions = await this.db.query(
        'SELECT * FROM decisions WHERE alert_id = ?',
        [alert.id]
      );

      res.json({
        success: true,
        data: {
          ...alert,
          decisions,
        },
      });
    } catch (error) {
      logger.error('Error fetching alert:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch alert',
      });
    }
  }

  // PUT /api/alerts/:id/status - Alert Status ändern
  async updateAlertStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status } = req.body;

      if (!['active', 'resolved', 'ignored'].includes(status)) {
        res.status(400).json({
          success: false,
          error: 'Invalid status. Must be: active, resolved, or ignored',
        });
        return;
      }

      const result = await this.db.run(
        'UPDATE alerts SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? OR crowdsec_id = ?',
        [status, id, id]
      );

      if (this.db.getType() === 'sqlite' && (result as any).changes === 0) {
        res.status(404).json({
          success: false,
          error: 'Alert not found',
        });
        return;
      }

      res.json({
        success: true,
        message: 'Alert status updated successfully',
      });
    } catch (error) {
      logger.error('Error updating alert status:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update alert status',
      });
    }
  }

  // GET /api/alerts/stats - Alert Statistiken
  async getAlertStats(req: Request, res: Response): Promise<void> {
    try {
      const { period = '24h' } = req.query;
      
      let timeFilter = '';
      switch (period) {
        case '1h':
          timeFilter = this.db.getType() === 'sqlite' 
            ? "start_at >= datetime('now', '-1 hour')"
            : "start_at >= NOW() - INTERVAL '1 hour'";
          break;
        case '24h':
          timeFilter = this.db.getType() === 'sqlite'
            ? "start_at >= datetime('now', '-24 hours')"
            : "start_at >= NOW() - INTERVAL '24 hours'";
          break;
        case '7d':
          timeFilter = this.db.getType() === 'sqlite'
            ? "start_at >= datetime('now', '-7 days')"
            : "start_at >= NOW() - INTERVAL '7 days'";
          break;
        case '30d':
          timeFilter = this.db.getType() === 'sqlite'
            ? "start_at >= datetime('now', '-30 days')"
            : "start_at >= NOW() - INTERVAL '30 days'";
          break;
        default:
          timeFilter = this.db.getType() === 'sqlite'
            ? "start_at >= datetime('now', '-24 hours')"
            : "start_at >= NOW() - INTERVAL '24 hours'";
      }

      const [
        totalAlerts,
        alertsBySeverity,
        alertsByScenario,
        alertsByCountry,
        recentTrend,
      ] = await Promise.all([
        // Total alerts
        this.db.get(`SELECT COUNT(*) as count FROM alerts WHERE ${timeFilter}`),
        
        // Alerts by severity
        this.db.query(`
          SELECT severity, COUNT(*) as count 
          FROM alerts 
          WHERE ${timeFilter}
          GROUP BY severity
        `),
        
        // Top scenarios
        this.db.query(`
          SELECT scenario, COUNT(*) as count 
          FROM alerts 
          WHERE ${timeFilter}
          GROUP BY scenario 
          ORDER BY count DESC 
          LIMIT 10
        `),
        
        // Top countries
        this.db.query(`
          SELECT source_country, COUNT(*) as count 
          FROM alerts 
          WHERE ${timeFilter} AND source_country IS NOT NULL
          GROUP BY source_country 
          ORDER BY count DESC 
          LIMIT 10
        `),
        
        // Hourly trend (last 24 hours)
        this.db.query(`
          SELECT 
            ${this.db.getType() === 'sqlite' 
              ? "strftime('%Y-%m-%d %H:00:00', start_at) as hour"
              : "date_trunc('hour', start_at) as hour"
            },
            COUNT(*) as count
          FROM alerts 
          WHERE ${this.db.getType() === 'sqlite'
            ? "start_at >= datetime('now', '-24 hours')"
            : "start_at >= NOW() - INTERVAL '24 hours'"
          }
          GROUP BY ${this.db.getType() === 'sqlite' 
            ? "strftime('%Y-%m-%d %H:00:00', start_at)"
            : "date_trunc('hour', start_at)"
          }
          ORDER BY hour
        `),
      ]);

      res.json({
        success: true,
        data: {
          total: totalAlerts?.count || 0,
          by_severity: alertsBySeverity,
          by_scenario: alertsByScenario,
          by_country: alertsByCountry,
          hourly_trend: recentTrend,
        },
      });
    } catch (error) {
      logger.error('Error fetching alert stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch alert statistics',
      });
    }
  }

  // POST /api/alerts/sync - Synchronisation mit CrowdSec
  async syncAlerts(req: Request, res: Response): Promise<void> {
    try {
      await this.crowdsecService.syncAlertsToDatabase();
      
      res.json({
        success: true,
        message: 'Alerts synchronized successfully',
      });
    } catch (error) {
      logger.error('Error syncing alerts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to sync alerts',
      });
    }
  }
}

export default new AlertsController();
