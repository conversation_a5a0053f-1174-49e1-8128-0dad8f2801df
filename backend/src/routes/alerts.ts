import { Router } from 'express';
import alertsController from '../controllers/alertsController';
import { validateQuery, validateParams } from '../middleware/validation';
import { body, param, query } from 'express-validator';

const router = Router();

// Validation schemas
const alertsQueryValidation = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['active', 'resolved', 'ignored']).withMessage('Invalid status'),
  query('severity').optional().isIn(['low', 'medium', 'high', 'critical']).withMessage('Invalid severity'),
  query('scenario').optional().isString().withMessage('Scenario must be a string'),
  query('source_ip').optional().isIP().withMessage('Invalid IP address'),
  query('start_date').optional().isISO8601().withMessage('Invalid start date format'),
  query('end_date').optional().isISO8601().withMessage('Invalid end date format'),
];

const alertIdValidation = [
  param('id').isString().notEmpty().withMessage('Alert ID is required'),
];

const updateStatusValidation = [
  body('status').isIn(['active', 'resolved', 'ignored']).withMessage('Invalid status'),
];

const statsQueryValidation = [
  query('period').optional().isIn(['1h', '24h', '7d', '30d']).withMessage('Invalid period'),
];

// Routes
router.get('/', alertsQueryValidation, validateQuery, alertsController.getAlerts.bind(alertsController));
router.get('/stats', statsQueryValidation, validateQuery, alertsController.getAlertStats.bind(alertsController));
router.get('/:id', alertIdValidation, validateParams, alertsController.getAlert.bind(alertsController));
router.put('/:id/status', alertIdValidation, updateStatusValidation, validateParams, alertsController.updateAlertStatus.bind(alertsController));
router.post('/sync', alertsController.syncAlerts.bind(alertsController));

export default router;
