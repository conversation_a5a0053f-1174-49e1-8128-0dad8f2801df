import axios, { AxiosInstance } from 'axios';
import { exec } from 'child_process';
import { promisify } from 'util';
import config from '../config';
import { getDatabase } from '../config/database';
import logger from '../utils/logger';

const execAsync = promisify(exec);

export interface CrowdSecAlert {
  id: string;
  scenario: string;
  message: string;
  source: {
    ip: string;
    country?: string;
    as_name?: string;
    as_number?: number;
  };
  events_count: number;
  capacity: number;
  leakspeed: string;
  simulated: boolean;
  start_at: string;
  stop_at?: string;
  machine_id: string;
}

export interface CrowdSecDecision {
  id: string;
  origin: string;
  type: string;
  scope: string;
  value: string;
  duration: string;
  until: string;
  scenario: string;
  simulated: boolean;
}

export interface CrowdSecBouncer {
  name: string;
  ip_address: string;
  valid: boolean;
  last_pull: string;
  type: string;
  version: string;
  auth_type: string;
}

export interface CrowdSecMetrics {
  engine: {
    parsers: number;
    scenarios: number;
    buckets: number;
    overflow_buckets: number;
  };
  acquisition: {
    reads: number;
    parsed: number;
    unparsed: number;
  };
  local_api: {
    alerts: number;
    decisions: number;
  };
}

export class CrowdSecService {
  private apiClient: AxiosInstance;
  private db: any;

  constructor() {
    this.db = getDatabase();
    this.apiClient = axios.create({
      baseURL: config.crowdsec.apiUrl,
      timeout: 10000,
      headers: {
        'User-Agent': 'crowdsec-dashboard/1.0.0',
        ...(config.crowdsec.apiKey && {
          'X-Api-Key': config.crowdsec.apiKey,
        }),
      },
    });

    // Request interceptor for logging
    this.apiClient.interceptors.request.use(
      (config) => {
        logger.debug(`CrowdSec API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('CrowdSec API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.apiClient.interceptors.response.use(
      (response) => {
        logger.debug(`CrowdSec API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        logger.error('CrowdSec API Response Error:', {
          url: error.config?.url,
          status: error.response?.status,
          message: error.message,
        });
        return Promise.reject(error);
      }
    );
  }

  // CLI-basierte Methoden für detaillierte Informationen
  async getAlertsFromCLI(limit = 100): Promise<CrowdSecAlert[]> {
    try {
      const { stdout } = await execAsync(`cscli alerts list -o json --limit ${limit}`);
      const alerts = JSON.parse(stdout);
      return Array.isArray(alerts) ? alerts : [];
    } catch (error) {
      logger.error('Error fetching alerts from CLI:', error);
      return [];
    }
  }

  async getDecisionsFromCLI(limit = 100): Promise<CrowdSecDecision[]> {
    try {
      const { stdout } = await execAsync(`cscli decisions list -o json --limit ${limit}`);
      const decisions = JSON.parse(stdout);
      return Array.isArray(decisions) ? decisions : [];
    } catch (error) {
      logger.error('Error fetching decisions from CLI:', error);
      return [];
    }
  }

  async getBouncersFromCLI(): Promise<CrowdSecBouncer[]> {
    try {
      const { stdout } = await execAsync('cscli bouncers list -o json');
      const bouncers = JSON.parse(stdout);
      return Array.isArray(bouncers) ? bouncers : [];
    } catch (error) {
      logger.error('Error fetching bouncers from CLI:', error);
      return [];
    }
  }

  async getMetricsFromCLI(): Promise<CrowdSecMetrics | null> {
    try {
      const { stdout } = await execAsync('cscli metrics -o json');
      return JSON.parse(stdout);
    } catch (error) {
      logger.error('Error fetching metrics from CLI:', error);
      return null;
    }
  }

  async getHubStatusFromCLI(): Promise<any> {
    try {
      const { stdout } = await execAsync('cscli hub list -o json');
      return JSON.parse(stdout);
    } catch (error) {
      logger.error('Error fetching hub status from CLI:', error);
      return null;
    }
  }

  // API-basierte Methoden
  async getAlertsFromAPI(): Promise<CrowdSecAlert[]> {
    try {
      const response = await this.apiClient.get('/v1/alerts');
      return response.data || [];
    } catch (error) {
      logger.error('Error fetching alerts from API:', error);
      return [];
    }
  }

  async getDecisionsFromAPI(): Promise<CrowdSecDecision[]> {
    try {
      const response = await this.apiClient.get('/v1/decisions');
      return response.data || [];
    } catch (error) {
      logger.error('Error fetching decisions from API:', error);
      return [];
    }
  }

  async deleteDecision(decisionId: string): Promise<boolean> {
    try {
      await this.apiClient.delete(`/v1/decisions/${decisionId}`);
      return true;
    } catch (error) {
      logger.error('Error deleting decision:', error);
      return false;
    }
  }

  async addDecision(decision: Partial<CrowdSecDecision>): Promise<boolean> {
    try {
      await this.apiClient.post('/v1/decisions', decision);
      return true;
    } catch (error) {
      logger.error('Error adding decision:', error);
      return false;
    }
  }

  // Synchronisation mit lokaler Datenbank
  async syncAlertsToDatabase(): Promise<void> {
    try {
      const alerts = await this.getAlertsFromCLI(1000);
      
      for (const alert of alerts) {
        await this.db.run(`
          INSERT OR REPLACE INTO alerts (
            crowdsec_id, scenario, message, source_ip, source_country, 
            source_as_name, source_as_number, events_count, capacity, 
            leakspeed, simulated, start_at, stop_at, machine_id
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          alert.id,
          alert.scenario,
          alert.message,
          alert.source.ip,
          alert.source.country,
          alert.source.as_name,
          alert.source.as_number,
          alert.events_count,
          alert.capacity,
          alert.leakspeed,
          alert.simulated ? 1 : 0,
          alert.start_at,
          alert.stop_at,
          alert.machine_id,
        ]);
      }

      logger.info(`Synced ${alerts.length} alerts to database`);
    } catch (error) {
      logger.error('Error syncing alerts to database:', error);
    }
  }

  async syncDecisionsToDatabase(): Promise<void> {
    try {
      const decisions = await this.getDecisionsFromCLI(1000);
      
      for (const decision of decisions) {
        await this.db.run(`
          INSERT OR REPLACE INTO decisions (
            crowdsec_id, type, scope, value, duration, scenario, 
            origin, simulated, until
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          decision.id,
          decision.type,
          decision.scope,
          decision.value,
          decision.duration,
          decision.scenario,
          decision.origin,
          decision.simulated ? 1 : 0,
          decision.until,
        ]);
      }

      logger.info(`Synced ${decisions.length} decisions to database`);
    } catch (error) {
      logger.error('Error syncing decisions to database:', error);
    }
  }

  async syncBouncersToDatabase(): Promise<void> {
    try {
      const bouncers = await this.getBouncersFromCLI();
      
      for (const bouncer of bouncers) {
        await this.db.run(`
          INSERT OR REPLACE INTO bouncers (
            name, type, ip_address, version, last_pull, is_active
          ) VALUES (?, ?, ?, ?, ?, ?)
        `, [
          bouncer.name,
          bouncer.type,
          bouncer.ip_address,
          bouncer.version,
          bouncer.last_pull,
          bouncer.valid ? 1 : 0,
        ]);
      }

      logger.info(`Synced ${bouncers.length} bouncers to database`);
    } catch (error) {
      logger.error('Error syncing bouncers to database:', error);
    }
  }

  async recordEngineMetrics(): Promise<void> {
    try {
      const metrics = await this.getMetricsFromCLI();
      if (!metrics) return;

      await this.db.run(`
        INSERT INTO engine_metrics (
          parsers_loaded, scenarios_loaded, buckets_current, buckets_overflow,
          alerts_generated, decisions_created, log_lines_processed, 
          log_lines_parsed, log_lines_unparsed
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        metrics.engine?.parsers || 0,
        metrics.engine?.scenarios || 0,
        metrics.engine?.buckets || 0,
        metrics.engine?.overflow_buckets || 0,
        metrics.local_api?.alerts || 0,
        metrics.local_api?.decisions || 0,
        metrics.acquisition?.reads || 0,
        metrics.acquisition?.parsed || 0,
        metrics.acquisition?.unparsed || 0,
      ]);

      logger.debug('Recorded engine metrics to database');
    } catch (error) {
      logger.error('Error recording engine metrics:', error);
    }
  }

  // Health Check
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      // Test CLI access
      const { stdout: version } = await execAsync('cscli version --output json');
      const versionInfo = JSON.parse(version);

      // Test API access
      let apiStatus = 'unknown';
      try {
        await this.apiClient.get('/v1/alerts?limit=1');
        apiStatus = 'connected';
      } catch {
        apiStatus = 'disconnected';
      }

      return {
        status: 'healthy',
        details: {
          version: versionInfo.version,
          api_status: apiStatus,
          cli_access: true,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          cli_access: false,
        },
      };
    }
  }
}

export default CrowdSecService;
