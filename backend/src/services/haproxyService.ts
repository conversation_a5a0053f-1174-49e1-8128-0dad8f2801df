import axios from 'axios';
import { exec } from 'child_process';
import { promisify } from 'util';
import { getDatabase } from '../config/database';
import config from '../config';
import logger from '../utils/logger';

const execAsync = promisify(exec);

interface HAProxyStats {
  pxname: string;
  svname: string;
  qcur: number;
  qmax: number;
  scur: number;
  smax: number;
  slim: number;
  stot: number;
  bin: number;
  bout: number;
  dreq: number;
  dresp: number;
  ereq: number;
  econ: number;
  eresp: number;
  wretr: number;
  wredis: number;
  status: string;
  weight: number;
  act: number;
  bck: number;
  chkfail: number;
  chkdown: number;
  lastchg: number;
  downtime: number;
  qlimit: number;
  pid: number;
  iid: number;
  sid: number;
  throttle: number;
  lbtot: number;
  tracked: number;
  type: number;
  rate: number;
  rate_lim: number;
  rate_max: number;
  check_status: string;
  check_code: number;
  check_duration: number;
  hrsp_1xx: number;
  hrsp_2xx: number;
  hrsp_3xx: number;
  hrsp_4xx: number;
  hrsp_5xx: number;
  hrsp_other: number;
  hanafail: number;
  req_rate: number;
  req_rate_max: number;
  req_tot: number;
  cli_abrt: number;
  srv_abrt: number;
  comp_in: number;
  comp_out: number;
  comp_byp: number;
  comp_rsp: number;
  lastsess: number;
  last_chk: string;
  last_agt: string;
  qtime: number;
  ctime: number;
  rtime: number;
  ttime: number;
}

interface CrowdSecLuaStats {
  blocked_requests: number;
  allowed_requests: number;
  total_requests: number;
  block_rate: number;
  response_time_avg: number;
  last_update: string;
}

class HAProxyService {
  private db: any;
  private statsUrl: string;
  private statsAuth?: { username: string; password: string };

  constructor() {
    this.db = getDatabase();
    this.statsUrl = config.haproxy?.statsUrl || 'http://localhost:8404/stats';
    if (config.haproxy?.statsAuth) {
      this.statsAuth = config.haproxy.statsAuth;
    }
  }

  // HAProxy Stats API abrufen
  async getHAProxyStats(): Promise<HAProxyStats[]> {
    try {
      const response = await axios.get(`${this.statsUrl};csv`, {
        auth: this.statsAuth,
        timeout: 5000,
      });

      const lines = response.data.split('\n');
      const headers = lines[0].replace('# ', '').split(',');
      const stats: HAProxyStats[] = [];

      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const values = line.split(',');
        const stat: any = {};

        headers.forEach((header: string, index: number) => {
          const value = values[index];
          stat[header] = isNaN(Number(value)) ? value : Number(value);
        });

        stats.push(stat as HAProxyStats);
      }

      return stats;
    } catch (error) {
      logger.error('Error fetching HAProxy stats:', error);
      throw error;
    }
  }

  // CrowdSec Lua Plugin Statistiken abrufen
  async getCrowdSecLuaStats(): Promise<CrowdSecLuaStats | null> {
    try {
      // Versuche über HAProxy Stats API
      const response = await axios.get(`${this.statsUrl}/crowdsec`, {
        auth: this.statsAuth,
        timeout: 5000,
      });

      return response.data;
    } catch (error) {
      logger.warn('CrowdSec Lua stats not available via API, trying log parsing');
      
      // Fallback: Parse HAProxy Logs für CrowdSec Informationen
      try {
        return await this.parseCrowdSecFromLogs();
      } catch (logError) {
        logger.error('Error parsing CrowdSec stats from logs:', logError);
        return null;
      }
    }
  }

  // Parse CrowdSec Statistiken aus HAProxy Logs
  private async parseCrowdSecFromLogs(): Promise<CrowdSecLuaStats | null> {
    try {
      const logPath = config.haproxy?.logPath || '/var/log/haproxy.log';
      
      // Lese die letzten 1000 Zeilen der HAProxy Logs
      const { stdout } = await execAsync(`tail -n 1000 ${logPath} | grep -E "(crowdsec|blocked|denied)"`);
      
      const lines = stdout.split('\n').filter(line => line.trim());
      let blockedCount = 0;
      let totalCount = lines.length;
      
      // Zähle blockierte Requests basierend auf Log-Patterns
      lines.forEach(line => {
        if (line.includes('denied') || line.includes('blocked') || line.includes('403')) {
          blockedCount++;
        }
      });

      const allowedCount = totalCount - blockedCount;
      const blockRate = totalCount > 0 ? (blockedCount / totalCount) * 100 : 0;

      return {
        blocked_requests: blockedCount,
        allowed_requests: allowedCount,
        total_requests: totalCount,
        block_rate: blockRate,
        response_time_avg: 0, // Nicht aus Logs verfügbar
        last_update: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('Error parsing HAProxy logs:', error);
      return null;
    }
  }

  // HAProxy Metriken in Datenbank speichern
  async saveMetricsToDatabase(stats: HAProxyStats[]): Promise<void> {
    try {
      const timestamp = new Date().toISOString();

      for (const stat of stats) {
        // Nur Frontend und Backend Statistiken speichern
        if (stat.type === 0 || stat.type === 1) {
          await this.db.run(`
            INSERT INTO haproxy_metrics (
              frontend_name, backend_name, server_name,
              requests_total, requests_blocked, bytes_in, bytes_out,
              response_time_avg, active_sessions, max_sessions,
              status, recorded_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            stat.pxname,
            stat.type === 1 ? stat.pxname : null,
            stat.svname || null,
            stat.stot || 0,
            0, // Wird später mit CrowdSec Daten ergänzt
            stat.bin || 0,
            stat.bout || 0,
            stat.ttime || 0,
            stat.scur || 0,
            stat.smax || 0,
            stat.status || 'UNKNOWN',
            timestamp,
          ]);
        }
      }

      logger.debug(`Saved ${stats.length} HAProxy metrics to database`);
    } catch (error) {
      logger.error('Error saving HAProxy metrics to database:', error);
      throw error;
    }
  }

  // CrowdSec Lua Statistiken in Datenbank aktualisieren
  async updateCrowdSecMetrics(luaStats: CrowdSecLuaStats): Promise<void> {
    try {
      const timestamp = new Date().toISOString();

      // Aktualisiere die neuesten HAProxy Metriken mit CrowdSec Daten
      await this.db.run(`
        UPDATE haproxy_metrics 
        SET requests_blocked = ?
        WHERE recorded_at >= datetime('now', '-5 minutes')
      `, [luaStats.blocked_requests]);

      logger.debug('Updated HAProxy metrics with CrowdSec data');
    } catch (error) {
      logger.error('Error updating CrowdSec metrics:', error);
      throw error;
    }
  }

  // Aggregierte HAProxy Statistiken abrufen
  async getAggregatedStats(period: string = '24h'): Promise<any> {
    try {
      let timeFilter = '';
      switch (period) {
        case '1h':
          timeFilter = this.db.getType() === 'sqlite'
            ? "recorded_at >= datetime('now', '-1 hour')"
            : "recorded_at >= NOW() - INTERVAL '1 hour'";
          break;
        case '24h':
          timeFilter = this.db.getType() === 'sqlite'
            ? "recorded_at >= datetime('now', '-24 hours')"
            : "recorded_at >= NOW() - INTERVAL '24 hours'";
          break;
        case '7d':
          timeFilter = this.db.getType() === 'sqlite'
            ? "recorded_at >= datetime('now', '-7 days')"
            : "recorded_at >= NOW() - INTERVAL '7 days'";
          break;
        default:
          timeFilter = this.db.getType() === 'sqlite'
            ? "recorded_at >= datetime('now', '-24 hours')"
            : "recorded_at >= NOW() - INTERVAL '24 hours'";
      }

      const [
        totalStats,
        frontendStats,
        timeSeriesData,
      ] = await Promise.all([
        // Gesamtstatistiken
        this.db.get(`
          SELECT 
            SUM(requests_total) as total_requests,
            SUM(requests_blocked) as total_blocked,
            SUM(bytes_in) as total_bytes_in,
            SUM(bytes_out) as total_bytes_out,
            AVG(response_time_avg) as avg_response_time,
            MAX(active_sessions) as max_active_sessions
          FROM haproxy_metrics 
          WHERE ${timeFilter}
        `),

        // Frontend Statistiken
        this.db.query(`
          SELECT 
            frontend_name,
            SUM(requests_total) as requests,
            SUM(requests_blocked) as blocked,
            AVG(response_time_avg) as avg_response_time,
            MAX(active_sessions) as max_sessions
          FROM haproxy_metrics 
          WHERE ${timeFilter} AND backend_name IS NULL
          GROUP BY frontend_name
        `),

        // Zeitreihen Daten
        this.db.query(`
          SELECT 
            ${this.db.getType() === 'sqlite' 
              ? "strftime('%Y-%m-%d %H:00:00', recorded_at)" 
              : "DATE_TRUNC('hour', recorded_at)"
            } as hour,
            SUM(requests_total) as requests,
            SUM(requests_blocked) as blocked
          FROM haproxy_metrics 
          WHERE ${timeFilter}
          GROUP BY hour
          ORDER BY hour
        `),
      ]);

      return {
        total: totalStats || {
          total_requests: 0,
          total_blocked: 0,
          total_bytes_in: 0,
          total_bytes_out: 0,
          avg_response_time: 0,
          max_active_sessions: 0,
        },
        frontends: frontendStats || [],
        timeseries: timeSeriesData || [],
      };
    } catch (error) {
      logger.error('Error getting aggregated HAProxy stats:', error);
      throw error;
    }
  }

  // Health Check für HAProxy
  async healthCheck(): Promise<{ status: string; message: string }> {
    try {
      const response = await axios.get(this.statsUrl, {
        auth: this.statsAuth,
        timeout: 5000,
      });

      return {
        status: 'healthy',
        message: 'HAProxy stats API is accessible',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        message: `HAProxy stats API not accessible: ${(error as Error).message}`,
      };
    }
  }

  // Synchronisiere alle HAProxy Daten
  async syncAllData(): Promise<void> {
    try {
      logger.info('Starting HAProxy data synchronization...');

      // HAProxy Statistiken abrufen und speichern
      const stats = await this.getHAProxyStats();
      await this.saveMetricsToDatabase(stats);

      // CrowdSec Lua Statistiken abrufen und aktualisieren
      const luaStats = await this.getCrowdSecLuaStats();
      if (luaStats) {
        await this.updateCrowdSecMetrics(luaStats);
      }

      logger.info('HAProxy data synchronization completed');
    } catch (error) {
      logger.error('Error during HAProxy data synchronization:', error);
      throw error;
    }
  }
}

export default HAProxyService;
