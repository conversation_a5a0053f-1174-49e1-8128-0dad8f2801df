import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import path from 'path';

import config from './config';
import { initializeDatabase } from './config/database';
import logger from './utils/logger';

// Routes and controllers will be imported after database initialization

class App {
  public app: express.Application;
  public server: any;
  public io: SocketIOServer;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: config.security.corsOrigin,
        methods: ['GET', 'POST'],
      },
    });

    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
    this.initializeSocketIO();
  }

  private initializeMiddleware(): void {
    // View engine setup
    this.app.set('view engine', 'ejs');
    this.app.set('views', path.join(__dirname, '../src/views'));

    // Static files
    this.app.use(express.static(path.join(__dirname, '../src/public')));

    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false, // Disable for CDN resources
    }));

    // CORS
    this.app.use(cors({
      origin: config.security.corsOrigin,
      credentials: true,
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: config.security.rateLimitMax,
      message: {
        success: false,
        error: 'Too many requests from this IP, please try again later.',
      },
    });
    this.app.use('/api/', limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Compression
    this.app.use(compression());

    // Logging
    if (config.app.env !== 'test') {
      this.app.use(morgan('combined', {
        stream: {
          write: (message: string) => {
            logger.info(message.trim());
          },
        },
      }));
    }
  }

  private initializeRoutes(): void {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        message: 'CrowdSec Dashboard API is running',
        version: config.app.version,
        timestamp: new Date().toISOString(),
      });
    });

    // Web routes (Dashboard UI)
    this.app.get('/', (req, res) => {
      res.render('dashboard', {
        title: 'Dashboard',
        currentPage: 'dashboard'
      });
    });

    this.app.get('/alerts', (req, res) => {
      res.render('alerts', {
        title: 'Alerts',
        currentPage: 'alerts'
      });
    });

    this.app.get('/decisions', (req, res) => {
      res.render('decisions', {
        title: 'Decisions',
        currentPage: 'decisions'
      });
    });

    this.app.get('/bouncers', (req, res) => {
      res.render('bouncers', {
        title: 'Bouncers',
        currentPage: 'bouncers'
      });
    });

    this.app.get('/haproxy', (req, res) => {
      res.render('haproxy', {
        title: 'HAProxy',
        currentPage: 'haproxy'
      });
    });

    // Helper function to create controller instances on demand
    const createController = (ControllerClass: any) => {
      return new ControllerClass();
    };

    // Alerts routes
    this.app.get('/api/alerts', (req, res) => {
      const { AlertsController } = require('./controllers/alertsController');
      const controller = createController(AlertsController);
      controller.getAlerts(req, res);
    });
    this.app.get('/api/alerts/stats', (req, res) => {
      const { AlertsController } = require('./controllers/alertsController');
      const controller = createController(AlertsController);
      controller.getAlertStats(req, res);
    });
    this.app.get('/api/alerts/:id', (req, res) => {
      const { AlertsController } = require('./controllers/alertsController');
      const controller = createController(AlertsController);
      controller.getAlert(req, res);
    });
    this.app.post('/api/alerts/sync', (req, res) => {
      const { AlertsController } = require('./controllers/alertsController');
      const controller = createController(AlertsController);
      controller.syncAlerts(req, res);
    });

    // Decisions routes
    this.app.get('/api/decisions', (req, res) => {
      const { DecisionsController } = require('./controllers/decisionsController');
      const controller = createController(DecisionsController);
      controller.getDecisions(req, res);
    });
    this.app.get('/api/decisions/stats', (req, res) => {
      const { DecisionsController } = require('./controllers/decisionsController');
      const controller = createController(DecisionsController);
      controller.getDecisionStats(req, res);
    });
    this.app.get('/api/decisions/:id', (req, res) => {
      const { DecisionsController } = require('./controllers/decisionsController');
      const controller = createController(DecisionsController);
      controller.getDecision(req, res);
    });
    this.app.post('/api/decisions', (req, res) => {
      const { DecisionsController } = require('./controllers/decisionsController');
      const controller = createController(DecisionsController);
      controller.createDecision(req, res);
    });
    this.app.delete('/api/decisions/:id', (req, res) => {
      const { DecisionsController } = require('./controllers/decisionsController');
      const controller = createController(DecisionsController);
      controller.deleteDecision(req, res);
    });
    this.app.post('/api/decisions/sync', (req, res) => {
      const { DecisionsController } = require('./controllers/decisionsController');
      const controller = createController(DecisionsController);
      controller.syncDecisions(req, res);
    });

    // Bouncers routes
    this.app.get('/api/bouncers', (req, res) => {
      const { BouncersController } = require('./controllers/bouncersController');
      const controller = createController(BouncersController);
      controller.getBouncers(req, res);
    });
    this.app.get('/api/bouncers/stats', (req, res) => {
      const { BouncersController } = require('./controllers/bouncersController');
      const controller = createController(BouncersController);
      controller.getBouncerStats(req, res);
    });
    this.app.get('/api/bouncers/:id', (req, res) => {
      const { BouncersController } = require('./controllers/bouncersController');
      const controller = createController(BouncersController);
      controller.getBouncer(req, res);
    });
    this.app.get('/api/bouncers/:id/metrics', (req, res) => {
      const { BouncersController } = require('./controllers/bouncersController');
      const controller = createController(BouncersController);
      controller.getBouncerMetrics(req, res);
    });
    this.app.post('/api/bouncers/sync', (req, res) => {
      const { BouncersController } = require('./controllers/bouncersController');
      const controller = createController(BouncersController);
      controller.syncBouncers(req, res);
    });

    // HAProxy routes
    this.app.get('/api/haproxy/stats', (req, res) => {
      const { HAProxyController } = require('./controllers/haproxyController');
      const controller = createController(HAProxyController);
      controller.getStats(req, res);
    });
    this.app.get('/api/haproxy/metrics', (req, res) => {
      const { HAProxyController } = require('./controllers/haproxyController');
      const controller = createController(HAProxyController);
      controller.getMetrics(req, res);
    });
    this.app.get('/api/haproxy/crowdsec', (req, res) => {
      const { HAProxyController } = require('./controllers/haproxyController');
      const controller = createController(HAProxyController);
      controller.getCrowdSecStats(req, res);
    });
    this.app.get('/api/haproxy/health', (req, res) => {
      const { HAProxyController } = require('./controllers/haproxyController');
      const controller = createController(HAProxyController);
      controller.getHealth(req, res);
    });
    this.app.get('/api/haproxy/frontends', (req, res) => {
      const { HAProxyController } = require('./controllers/haproxyController');
      const controller = createController(HAProxyController);
      controller.getFrontends(req, res);
    });
    this.app.get('/api/haproxy/backends', (req, res) => {
      const { HAProxyController } = require('./controllers/haproxyController');
      const controller = createController(HAProxyController);
      controller.getBackends(req, res);
    });
    this.app.get('/api/haproxy/servers', (req, res) => {
      const { HAProxyController } = require('./controllers/haproxyController');
      const controller = createController(HAProxyController);
      controller.getServers(req, res);
    });
    this.app.get('/api/haproxy/dashboard', (req, res) => {
      const { HAProxyController } = require('./controllers/haproxyController');
      const controller = createController(HAProxyController);
      controller.getDashboardData(req, res);
    });
    this.app.post('/api/haproxy/sync', (req, res) => {
      const { HAProxyController } = require('./controllers/haproxyController');
      const controller = createController(HAProxyController);
      controller.syncData(req, res);
    });

    // 404 handler for API routes only
    this.app.use('/api/*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'API route not found',
      });
    });

    // 404 handler for other routes
    this.app.use('*', (req, res) => {
      res.status(404).render('404', {
        title: '404 - Page Not Found',
        currentPage: '404'
      });
    });
  }

  private initializeErrorHandling(): void {
    // Global error handler
    this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Unhandled error:', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
      });

      res.status(500).json({
        success: false,
        error: config.app.env === 'production' 
          ? 'Internal server error' 
          : error.message,
      });
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });
  }

  private initializeSocketIO(): void {
    this.io.on('connection', (socket) => {
      logger.info(`Client connected: ${socket.id}`);

      socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.id}`);
      });

      // Join rooms for real-time updates
      socket.on('join_alerts', () => {
        socket.join('alerts');
        logger.debug(`Client ${socket.id} joined alerts room`);
      });

      socket.on('join_decisions', () => {
        socket.join('decisions');
        logger.debug(`Client ${socket.id} joined decisions room`);
      });

      socket.on('join_metrics', () => {
        socket.join('metrics');
        logger.debug(`Client ${socket.id} joined metrics room`);
      });
    });
  }

  public async start(): Promise<void> {
    try {
      // Initialize database
      const dbConfig = config.database.type === 'postgresql' && config.database.postgresql
        ? { type: 'postgresql' as const, postgresql: config.database.postgresql }
        : { type: 'sqlite' as const, sqlite: config.database.sqlite };

      const db = initializeDatabase(dbConfig);
      await db.connect();

      // Test CrowdSec connection
      const CrowdSecService = require('./services/crowdsecService').default;
      const crowdsecService = new CrowdSecService();
      const healthCheck = await crowdsecService.healthCheck();
      logger.info('CrowdSec Health Check:', healthCheck);

      // Start server
      this.server.listen(config.app.port, () => {
        logger.info(`🚀 ${config.app.name} started on port ${config.app.port}`);
        logger.info(`📊 Environment: ${config.app.env}`);
        logger.info(`🔗 API URL: http://localhost:${config.app.port}`);
        logger.info(`🔗 Health Check: http://localhost:${config.app.port}/health`);
      });

      // Graceful shutdown
      process.on('SIGTERM', () => this.shutdown());
      process.on('SIGINT', () => this.shutdown());

    } catch (error) {
      logger.error('Failed to start application:', error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    logger.info('Shutting down gracefully...');
    
    this.server.close(() => {
      logger.info('HTTP server closed');
    });

    // Close database connection
    try {
      const db = require('./config/database').getDatabase();
      await db.disconnect();
    } catch (error) {
      logger.error('Error closing database connection:', error);
    }

    process.exit(0);
  }
}

// Start the application
const app = new App();
app.start().catch((error) => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});

export default app;
