// Dashboard JavaScript

// Global variables
let alertTrendsChart = null;
let refreshInterval = null;

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    startAutoRefresh();
});

// Initialize dashboard
function initializeDashboard() {
    loadDashboardStats();
    loadRecentAlerts();
    loadSystemStatus();
    initializeCharts();
}

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        // Load alerts count
        const alertsResponse = await fetch('/api/alerts/stats');
        if (alertsResponse.ok) {
            const alertsData = await alertsResponse.json();
            updateStatsCard('alerts-count', alertsData.data?.total || 0);
        } else {
            updateStatsCard('alerts-count', 'N/A');
        }

        // Load decisions count
        const decisionsResponse = await fetch('/api/decisions/stats');
        if (decisionsResponse.ok) {
            const decisionsData = await decisionsResponse.json();
            updateStatsCard('decisions-count', decisionsData.data?.active || 0);
        } else {
            updateStatsCard('decisions-count', 'N/A');
        }

        // Load bouncers count
        const bouncersResponse = await fetch('/api/bouncers/stats');
        if (bouncersResponse.ok) {
            const bouncersData = await bouncersResponse.json();
            updateStatsCard('bouncers-count', bouncersData.data?.active || 0);
        } else {
            updateStatsCard('bouncers-count', 'N/A');
        }

        // Load blocked IPs count (mock data for now)
        updateStatsCard('blocked-ips-count', Math.floor(Math.random() * 1000));

    } catch (error) {
        console.error('Error loading dashboard stats:', error);
        updateStatsCard('alerts-count', 'Error');
        updateStatsCard('decisions-count', 'Error');
        updateStatsCard('bouncers-count', 'Error');
        updateStatsCard('blocked-ips-count', 'Error');
    }
}

// Update stats card
function updateStatsCard(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = value;
        element.classList.add('fade-in');
    }
}

// Load recent alerts
async function loadRecentAlerts() {
    try {
        const response = await fetch('/api/alerts?limit=5');
        const alertsContainer = document.getElementById('recent-alerts');
        
        if (response.ok) {
            const data = await response.json();
            const alerts = data.data || [];
            
            if (alerts.length > 0) {
                alertsContainer.innerHTML = alerts.map(alert => `
                    <div class="alert-item ${getSeverityClass(alert.severity)}">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-${getSeverityColor(alert.severity)}-500"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                                ${alert.scenario || 'Unknown Scenario'}
                            </p>
                            <p class="text-sm text-gray-500">
                                <span class="ip-address">${alert.source_ip || 'Unknown IP'}</span>
                                <span class="timestamp ml-2">${formatTimestamp(alert.created_at)}</span>
                            </p>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="status-badge severity-${alert.severity || 'info'}">
                                ${alert.severity || 'info'}
                            </span>
                        </div>
                    </div>
                `).join('');
            } else {
                alertsContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No recent alerts</p>';
            }
        } else {
            alertsContainer.innerHTML = '<p class="text-danger-500 text-center py-4">Failed to load alerts</p>';
        }
    } catch (error) {
        console.error('Error loading recent alerts:', error);
        document.getElementById('recent-alerts').innerHTML = '<p class="text-danger-500 text-center py-4">Error loading alerts</p>';
    }
}

// Load system status
async function loadSystemStatus() {
    const statusContainer = document.getElementById('system-status');
    
    // Check CrowdSec Engine
    try {
        const response = await fetch('/api/alerts/stats');
        updateSystemStatus('CrowdSec Engine', response.ok ? 'success' : 'danger', response.ok ? 'Running' : 'Error');
    } catch (error) {
        updateSystemStatus('CrowdSec Engine', 'danger', 'Offline');
    }

    // Check HAProxy
    try {
        const response = await fetch('/api/haproxy/health');
        const data = await response.json();
        const isHealthy = data.success && data.data?.status === 'healthy';
        updateSystemStatus('HAProxy', isHealthy ? 'success' : 'warning', isHealthy ? 'Online' : 'Limited');
    } catch (error) {
        updateSystemStatus('HAProxy', 'warning', 'Unknown');
    }

    // Database is always connected if we can load the page
    updateSystemStatus('Database', 'success', 'Connected');
}

// Update system status
function updateSystemStatus(service, status, text) {
    const statusContainer = document.getElementById('system-status');
    const serviceRow = Array.from(statusContainer.children).find(row => 
        row.querySelector('span').textContent === service
    );
    
    if (serviceRow) {
        const statusBadge = serviceRow.querySelector('.status-badge');
        statusBadge.className = `status-badge status-${status}`;
        statusBadge.innerHTML = `<i class="fas fa-${getStatusIcon(status)} text-${status}-500 mr-1"></i>${text}`;
    }
}

// Initialize charts
function initializeCharts() {
    const ctx = document.getElementById('alertTrendsChart');
    if (ctx) {
        // Mock data for alert trends
        const mockData = {
            labels: ['7 days ago', '6 days ago', '5 days ago', '4 days ago', '3 days ago', '2 days ago', 'Yesterday'],
            datasets: [{
                label: 'Alerts',
                data: [12, 19, 8, 15, 22, 18, 25],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        };

        alertTrendsChart = new Chart(ctx, {
            type: 'line',
            data: mockData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
}

// Utility functions
function getSeverityClass(severity) {
    switch (severity?.toLowerCase()) {
        case 'high': return 'high';
        case 'medium': return 'medium';
        case 'low': return 'low';
        default: return '';
    }
}

function getSeverityColor(severity) {
    switch (severity?.toLowerCase()) {
        case 'high': return 'danger';
        case 'medium': return 'warning';
        case 'low': return 'success';
        default: return 'primary';
    }
}

function getStatusIcon(status) {
    switch (status) {
        case 'success': return 'check-circle';
        case 'warning': return 'exclamation-triangle';
        case 'danger': return 'times-circle';
        default: return 'question-circle';
    }
}

function formatTimestamp(timestamp) {
    if (!timestamp) return 'Unknown';
    const date = new Date(timestamp);
    return date.toLocaleString();
}

// Action functions
function refreshData() {
    const refreshBtn = document.querySelector('[onclick="refreshData()"]');
    const icon = refreshBtn.querySelector('i');
    
    // Add spinning animation
    icon.classList.add('fa-spin');
    
    // Reload all data
    loadDashboardStats();
    loadRecentAlerts();
    loadSystemStatus();
    
    // Remove spinning animation after 1 second
    setTimeout(() => {
        icon.classList.remove('fa-spin');
    }, 1000);
}

async function syncAlerts() {
    try {
        const response = await fetch('/api/alerts/sync', { method: 'POST' });
        if (response.ok) {
            showNotification('Alerts synced successfully', 'success');
            loadDashboardStats();
            loadRecentAlerts();
        } else {
            showNotification('Failed to sync alerts', 'error');
        }
    } catch (error) {
        showNotification('Error syncing alerts', 'error');
    }
}

async function syncDecisions() {
    try {
        const response = await fetch('/api/decisions/sync', { method: 'POST' });
        if (response.ok) {
            showNotification('Decisions synced successfully', 'success');
            loadDashboardStats();
        } else {
            showNotification('Failed to sync decisions', 'error');
        }
    } catch (error) {
        showNotification('Error syncing decisions', 'error');
    }
}

async function syncBouncers() {
    try {
        const response = await fetch('/api/bouncers/sync', { method: 'POST' });
        if (response.ok) {
            showNotification('Bouncers synced successfully', 'success');
            loadDashboardStats();
        } else {
            showNotification('Failed to sync bouncers', 'error');
        }
    } catch (error) {
        showNotification('Error syncing bouncers', 'error');
    }
}

function exportData() {
    // Mock export functionality
    showNotification('Export functionality coming soon', 'info');
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white ${
        type === 'success' ? 'bg-success-500' :
        type === 'error' ? 'bg-danger-500' :
        type === 'warning' ? 'bg-warning-500' :
        'bg-primary-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Auto refresh
function startAutoRefresh() {
    // Refresh every 30 seconds
    refreshInterval = setInterval(() => {
        loadDashboardStats();
        loadRecentAlerts();
        loadSystemStatus();
    }, 30000);
}

// Mobile sidebar toggle
function toggleSidebar() {
    const overlay = document.getElementById('sidebar-overlay');
    overlay.classList.toggle('hidden');
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
