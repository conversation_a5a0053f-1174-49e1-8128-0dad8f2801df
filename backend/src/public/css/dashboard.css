/* Custom Dashboard Styles */

/* Navigation */
.nav-link {
    @apply group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900;
}

.nav-link.active {
    @apply bg-primary-50 text-primary-700 border-r-2 border-primary-600;
}

.nav-link i {
    @apply mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500;
}

.nav-link.active i {
    @apply text-primary-600;
}

/* Stats Cards */
.stats-card {
    @apply transition-all duration-200 hover:shadow-lg;
}

.stats-card:hover {
    transform: translateY(-2px);
}

/* Status Badges */
.status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-success {
    @apply bg-success-100 text-success-800;
}

.status-warning {
    @apply bg-warning-100 text-warning-800;
}

.status-danger {
    @apply bg-danger-100 text-danger-800;
}

.status-loading {
    @apply bg-gray-100 text-gray-800;
}

/* Buttons */
.btn-primary {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200;
}

.btn-secondary {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200;
}

.btn-danger {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-danger-600 hover:bg-danger-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-danger-500 transition-colors duration-200;
}

.btn-success {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-success-600 hover:bg-success-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-success-500 transition-colors duration-200;
}

/* Tables */
.table {
    @apply min-w-full divide-y divide-gray-200;
}

.table thead {
    @apply bg-gray-50;
}

.table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table tbody tr {
    @apply hover:bg-gray-50 transition-colors duration-150;
}

/* Alert Items */
.alert-item {
    @apply flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors duration-150;
}

.alert-item.high {
    @apply border-danger-200 bg-danger-50;
}

.alert-item.medium {
    @apply border-warning-200 bg-warning-50;
}

.alert-item.low {
    @apply border-success-200 bg-success-50;
}

/* Loading States */
.loading-skeleton {
    @apply animate-pulse bg-gray-300 rounded;
}

/* Charts */
.chart-container {
    @apply relative h-64 w-full;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-card {
        @apply mb-4;
    }
    
    .nav-link {
        @apply px-4 py-3;
    }
    
    .btn-primary,
    .btn-secondary,
    .btn-danger,
    .btn-success {
        @apply w-full justify-center mb-2;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
}

::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Severity Colors */
.severity-high {
    @apply text-danger-600 bg-danger-100;
}

.severity-medium {
    @apply text-warning-600 bg-warning-100;
}

.severity-low {
    @apply text-success-600 bg-success-100;
}

.severity-info {
    @apply text-primary-600 bg-primary-100;
}

/* IP Address Styling */
.ip-address {
    @apply font-mono text-sm bg-gray-100 px-2 py-1 rounded;
}

/* Timestamp Styling */
.timestamp {
    @apply text-xs text-gray-500;
}

/* Card Hover Effects */
.card-hover {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

/* Focus States */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        @apply bg-white text-black;
    }
    
    .stats-card,
    .bg-white {
        @apply shadow-none border border-gray-300;
    }
}
