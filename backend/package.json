{"name": "crowdsec-dashboard-backend", "version": "1.0.0", "description": "Backend API for CrowdSec Dashboard", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "node dist/scripts/migrate.js", "db:seed": "node dist/scripts/seed.js"}, "keywords": ["crowdsec", "dashboard", "api", "security"], "author": "NK-IT", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "ejs": "^3.1.10", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "node-cron": "^3.0.3", "pg": "^8.11.3", "redis": "^4.6.10", "socket.io": "^4.7.4", "sqlite3": "^5.1.6", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "@types/pg": "^8.10.9", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}