# CrowdSec Local Dashboard

Ein modernes Dashboard für CrowdSec mit HAProxy-Integration zur Anzeige von Alerts, Decisions, Engine-Status und Bouncers.

## 🏗️ Architektur

```
crowdsec-local-dashboard/
├── backend/                 # Node.js/Express Backend mit integriertem Frontend
│   ├── src/
│   │   ├── controllers/    # API Controller
│   │   ├── services/       # Business Logic
│   │   ├── middleware/     # Express Middleware
│   │   ├── routes/         # API Routes
│   │   ├── views/          # EJS Templates (Frontend)
│   │   ├── public/         # Statische Dateien (CSS, JS)
│   │   └── utils/          # Utilities
│   ├── config/             # Konfiguration
│   └── package.json
├── database/               # Datenbankschemas und Migrationen
│   ├── migrations/
│   ├── seeds/
│   └── schema.sql
├── docs/                   # Dokumentation
└── scripts/               # Deployment und Utility Scripts
```

## 🚀 Features

- **Real-time Dashboard**: Live-Updates von CrowdSec Daten
- **Alerts Management**: Anzeige und Verwaltung von Security Alerts
- **Decisions Tracking**: Übersicht über aktive und historische Decisions
- **Bouncer Status**: Monitoring aller registrierten Bouncers
- **Engine Metrics**: Performance und Status des CrowdSec Engines
- **HAProxy Integration**: Integration mit HAProxy Lua Plugin
- **Historical Data**: PostgreSQL-basierte Datenspeicherung für Trends
- **Responsive Design**: Moderne, mobile-freundliche Benutzeroberfläche

## 🔧 Technologie-Stack

### Frontend
- **HTML/CSS/JavaScript** mit modernem Design
- **EJS Templates** für Server-Side Rendering
- **Tailwind CSS** via CDN für Styling
- **Chart.js** via CDN für Datenvisualisierung
- **Font Awesome** via CDN für Icons
- **Socket.io-client** für Real-time Updates

### Backend
- **Node.js** mit TypeScript
- **Express.js** für REST API und Web-Server
- **EJS** Template Engine
- **Socket.io** für WebSocket-Verbindungen
- **PostgreSQL** für historische Daten
- **SQLite** Integration für CrowdSec lokale DB
- **node-cron** für geplante Tasks

### Integration
- **CrowdSec Local API** (HTTP REST)
- **CrowdSec CLI** für detaillierte Abfragen
- **HAProxy Stats API**
- **PostgreSQL** für Datenhistorie

## 📊 Dashboard Komponenten

1. **Overview Dashboard**
   - Aktuelle Bedrohungslage
   - System-Status
   - Wichtige Metriken

2. **Alerts View**
   - Live-Alerts
   - Alert-Historie
   - Filterung und Suche

3. **Decisions Management**
   - Aktive Decisions
   - Decision-Historie
   - Manual Decision Management

4. **Bouncers Status**
   - Registrierte Bouncers
   - Status und Performance
   - Konfiguration

5. **Engine Metrics**
   - Performance-Metriken
   - Parsing-Statistiken
   - Scenario-Ausführung

6. **HAProxy Integration**
   - Traffic-Statistiken
   - Blocked Requests
   - Performance-Metriken

## 🛠️ Installation

### Schnellstart mit Docker

```bash
# Repository klonen
git clone <repository-url>
cd crowdsec-local-dashboard

# Umgebungsvariablen konfigurieren
cp .env.example .env
# Bearbeite .env mit deinen Einstellungen

# Services starten
docker compose up -d

# Dashboard öffnen
open http://localhost:3001
```

### Manuelle Installation

```bash
# Backend installieren und starten
cd backend
npm install
npm run build
npm start

# Dashboard ist verfügbar unter http://localhost:3001
```

## 📖 Dokumentation

- [API Documentation](docs/api.md)
- [Database Schema](docs/database.md)
- [Configuration Guide](docs/configuration.md)
- [Development Guide](docs/development.md)
- [Deployment Guide](docs/deployment.md)

## 🔒 Sicherheit

- API-Authentifizierung über JWT
- Rate Limiting
- Input Validation
- CORS-Konfiguration
- Sichere Datenbankverbindungen

## 🚀 Entwicklung

```bash
# Backend entwickeln
cd backend
npm install
npm run dev

# Dashboard ist verfügbar unter http://localhost:3001
# API-Endpunkte unter http://localhost:3001/api/
```

## 🌐 Zugriff

- **Dashboard**: http://localhost:3001
- **API**: http://localhost:3001/api/
- **Health Check**: http://localhost:3001/health

## 📝 Lizenz

MIT License - siehe [LICENSE](LICENSE) für Details.
