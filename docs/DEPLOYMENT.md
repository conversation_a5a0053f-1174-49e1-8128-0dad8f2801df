# Deployment Guide

Dieses Dokument beschreibt verschiedene Deployment-Optionen für das CrowdSec Dashboard.

## 🐳 Docker Deployment (Empfohlen)

### Produktions-Deployment mit Docker Compose

1. **Repository klonen und konfigurieren:**

```bash
git clone https://github.com/your-org/crowdsec-dashboard.git
cd crowdsec-dashboard

# Produktions-Konfiguration erstellen
cp .env.example .env
nano .env
```

2. **Wichtige Produktions-Einstellungen:**

```bash
# .env
NODE_ENV=production
DB_TYPE=postgresql
POSTGRES_PASSWORD=your_very_secure_password
JWT_SECRET=your-super-secret-jwt-key-64-chars-minimum
SESSION_SECRET=your-super-secret-session-key-64-chars-minimum
CROWDSEC_API_KEY=your_actual_crowdsec_api_key
```

3. **Services starten:**

```bash
# Produktions-Services starten
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Status überprüfen
docker-compose ps
```

4. **SSL/TLS mit Nginx Reverse Proxy:**

```nginx
# /etc/nginx/sites-available/crowdsec-dashboard
server {
    listen 80;
    server_name dashboard.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name dashboard.example.com;
    
    ssl_certificate /etc/letsencrypt/live/dashboard.example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dashboard.example.com/privkey.pem;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    
    # Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
    }
    
    # WebSocket
    location /socket.io/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Rate limiting
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
}
```

## 🖥️ Bare Metal Deployment

### Systemd Services

1. **Backend Service:**

```ini
# /etc/systemd/system/crowdsec-dashboard-backend.service
[Unit]
Description=CrowdSec Dashboard Backend
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=crowdsec-dashboard
WorkingDirectory=/opt/crowdsec-dashboard/backend
Environment=NODE_ENV=production
EnvironmentFile=/opt/crowdsec-dashboard/.env
ExecStart=/usr/bin/node dist/index.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

2. **Frontend Service:**

```ini
# /etc/systemd/system/crowdsec-dashboard-frontend.service
[Unit]
Description=CrowdSec Dashboard Frontend
After=network.target

[Service]
Type=simple
User=crowdsec-dashboard
WorkingDirectory=/opt/crowdsec-dashboard/frontend
Environment=NODE_ENV=production
EnvironmentFile=/opt/crowdsec-dashboard/.env
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

3. **Services aktivieren:**

```bash
sudo systemctl daemon-reload
sudo systemctl enable crowdsec-dashboard-backend
sudo systemctl enable crowdsec-dashboard-frontend
sudo systemctl start crowdsec-dashboard-backend
sudo systemctl start crowdsec-dashboard-frontend
```

## ☸️ Kubernetes Deployment

### Namespace und ConfigMap

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: crowdsec-dashboard
---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: crowdsec-dashboard-config
  namespace: crowdsec-dashboard
data:
  NODE_ENV: "production"
  DB_TYPE: "postgresql"
  POSTGRES_HOST: "postgresql"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "crowdsec_dashboard"
  REDIS_HOST: "redis"
  REDIS_PORT: "6379"
  CROWDSEC_API_URL: "http://127.0.0.1:8080"
```

### Secrets

```yaml
# k8s/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: crowdsec-dashboard-secrets
  namespace: crowdsec-dashboard
type: Opaque
data:
  postgres-password: <base64-encoded-password>
  jwt-secret: <base64-encoded-jwt-secret>
  session-secret: <base64-encoded-session-secret>
  crowdsec-api-key: <base64-encoded-api-key>
```

### PostgreSQL Deployment

```yaml
# k8s/postgresql.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql
  namespace: crowdsec-dashboard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql
  template:
    metadata:
      labels:
        app: postgresql
    spec:
      containers:
      - name: postgresql
        image: postgres:15
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: crowdsec-dashboard-config
              key: POSTGRES_DB
        - name: POSTGRES_USER
          value: "crowdsec"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: crowdsec-dashboard-secrets
              key: postgres-password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgresql
  namespace: crowdsec-dashboard
spec:
  selector:
    app: postgresql
  ports:
  - port: 5432
    targetPort: 5432
```

### Backend Deployment

```yaml
# k8s/backend.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: crowdsec-dashboard-backend
  namespace: crowdsec-dashboard
spec:
  replicas: 2
  selector:
    matchLabels:
      app: crowdsec-dashboard-backend
  template:
    metadata:
      labels:
        app: crowdsec-dashboard-backend
    spec:
      containers:
      - name: backend
        image: crowdsec-dashboard-backend:latest
        envFrom:
        - configMapRef:
            name: crowdsec-dashboard-config
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: crowdsec-dashboard-secrets
              key: postgres-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: crowdsec-dashboard-secrets
              key: jwt-secret
        ports:
        - containerPort: 3001
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: crowdsec-dashboard-backend
  namespace: crowdsec-dashboard
spec:
  selector:
    app: crowdsec-dashboard-backend
  ports:
  - port: 3001
    targetPort: 3001
```

## 🔄 CI/CD Pipeline

### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy CrowdSec Dashboard

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd backend && npm ci
        cd ../frontend && npm ci
    
    - name: Run tests
      run: |
        cd backend && npm test
        cd ../frontend && npm test
    
    - name: Build applications
      run: |
        cd backend && npm run build
        cd ../frontend && npm run build

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Build and push Backend
      uses: docker/build-push-action@v4
      with:
        context: ./backend
        push: true
        tags: ghcr.io/${{ github.repository }}/backend:latest
    
    - name: Build and push Frontend
      uses: docker/build-push-action@v4
      with:
        context: ./frontend
        push: true
        tags: ghcr.io/${{ github.repository }}/frontend:latest

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /opt/crowdsec-dashboard
          docker-compose pull
          docker-compose up -d
          docker system prune -f
```

## 📊 Monitoring und Logging

### Prometheus Monitoring

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'crowdsec-dashboard-backend'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
    
  - job_name: 'crowdsec-dashboard-postgres'
    static_configs:
      - targets: ['localhost:9187']
```

### Grafana Dashboard

```json
{
  "dashboard": {
    "title": "CrowdSec Dashboard Monitoring",
    "panels": [
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends"
          }
        ]
      }
    ]
  }
}
```

## 🔐 Security Hardening

### Firewall Konfiguration

```bash
# UFW Firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# Fail2ban für zusätzlichen Schutz
sudo apt install fail2ban
```

### Docker Security

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  backend:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
    user: "1001:1001"
    
  frontend:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/cache/nginx
    user: "1001:1001"
```

## 📈 Performance Optimierung

### Database Tuning

```sql
-- PostgreSQL Optimierungen
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;
SELECT pg_reload_conf();
```

### Nginx Caching

```nginx
# Nginx Caching Konfiguration
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=dashboard_cache:10m max_size=1g inactive=60m use_temp_path=off;

location /api/ {
    proxy_cache dashboard_cache;
    proxy_cache_valid 200 5m;
    proxy_cache_use_stale error timeout invalid_header updating http_500 http_502 http_503 http_504;
    proxy_cache_background_update on;
    proxy_cache_lock on;
    
    add_header X-Cache-Status $upstream_cache_status;
}
```

## 🔄 Backup und Recovery

### Automatisierte Backups

```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/crowdsec-dashboard"
DATE=$(date +%Y%m%d_%H%M%S)

# Database Backup
docker-compose exec -T postgres pg_dump -U crowdsec crowdsec_dashboard | gzip > "$BACKUP_DIR/db_$DATE.sql.gz"

# Configuration Backup
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" .env docker-compose.yml

# Cleanup old backups (keep 7 days)
find "$BACKUP_DIR" -name "*.gz" -mtime +7 -delete

# Cron Job: 0 2 * * * /opt/crowdsec-dashboard/backup.sh
```

### Recovery Prozedur

```bash
# Database Recovery
gunzip -c backup/db_20240115_020000.sql.gz | docker-compose exec -T postgres psql -U crowdsec -d crowdsec_dashboard

# Configuration Recovery
tar -xzf backup/config_20240115_020000.tar.gz

# Services restart
docker-compose down
docker-compose up -d
```
