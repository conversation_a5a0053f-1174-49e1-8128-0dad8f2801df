# Development Guide

Dieses Dokument beschreibt die Entwicklungsumgebung und -prozesse für das CrowdSec Dashboard.

## 🛠 Entwicklungsumgebung einrichten

### Voraussetzungen

- **Node.js** >= 18.0.0
- **npm** >= 8.0.0
- **Docker** >= 20.10.0 (optional, für lokale Services)
- **Git**
- **VS Code** (empfohlen)

### Repository klonen

```bash
git clone https://github.com/your-org/crowdsec-dashboard.git
cd crowdsec-dashboard
```

### Abhängigkeiten installieren

```bash
# Root-Abhängigkeiten (Workspace)
npm install

# Backend-Abhängigkeiten
cd backend && npm install

# Frontend-Abhängigkeiten
cd ../frontend && npm install
```

### Entwicklungsumgebung konfigurieren

```bash
# Entwicklungs-Konfiguration erstellen
cp .env.example .env.development

# Für lokale Entwicklung anpassen
nano .env.development
```

**Entwicklungs-Konfiguration:**

```bash
# .env.development
NODE_ENV=development
DB_TYPE=sqlite
SQLITE_DB_PATH=./data/dashboard.db
CROWDSEC_API_URL=http://127.0.0.1:8080
CROWDSEC_API_KEY=your_dev_api_key
LOG_LEVEL=debug
```

### Lokale Services starten

```bash
# Option 1: Mit Docker (empfohlen)
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Option 2: Lokale Installation
# PostgreSQL und Redis lokal installieren
```

### Entwicklungsserver starten

```bash
# Backend (Terminal 1)
cd backend
npm run dev

# Frontend (Terminal 2)
cd frontend
npm run dev
```

Das Dashboard ist dann unter http://localhost:3000 erreichbar.

## 🏗 Projektstruktur

```
crowdsec-dashboard/
├── backend/                    # Node.js/Express Backend
│   ├── src/
│   │   ├── controllers/       # API Controller
│   │   │   ├── alertsController.ts
│   │   │   ├── decisionsController.ts
│   │   │   ├── bouncersController.ts
│   │   │   └── haproxyController.ts
│   │   ├── services/          # Business Logic
│   │   │   ├── crowdsecService.ts
│   │   │   ├── haproxyService.ts
│   │   │   └── databaseService.ts
│   │   ├── middleware/        # Express Middleware
│   │   │   ├── auth.ts
│   │   │   ├── validation.ts
│   │   │   └── rateLimit.ts
│   │   ├── config/           # Konfiguration
│   │   │   ├── database.ts
│   │   │   └── index.ts
│   │   ├── utils/            # Utilities
│   │   │   ├── logger.ts
│   │   │   └── helpers.ts
│   │   └── index.ts          # Entry Point
│   ├── tests/                # Tests
│   ├── package.json
│   └── tsconfig.json
├── frontend/                  # React/Next.js Frontend
│   ├── src/
│   │   ├── components/       # React Komponenten
│   │   │   ├── Layout/
│   │   │   ├── Dashboard/
│   │   │   ├── Alerts/
│   │   │   └── Charts/
│   │   ├── pages/           # Next.js Pages
│   │   ├── hooks/           # Custom React Hooks
│   │   ├── services/        # API Services
│   │   ├── types/           # TypeScript Types
│   │   └── utils/           # Utilities
│   ├── public/              # Static Assets
│   ├── package.json
│   └── next.config.js
├── database/                 # Database Schema
│   ├── schema.sql
│   ├── migrations/
│   └── seeds/
├── docs/                    # Dokumentation
├── docker/                  # Docker Konfiguration
└── scripts/                # Utility Scripts
```

## 🧪 Testing

### Backend Tests

```bash
cd backend

# Unit Tests
npm test

# Integration Tests
npm run test:integration

# Test Coverage
npm run test:coverage

# Watch Mode
npm run test:watch
```

### Frontend Tests

```bash
cd frontend

# Unit Tests
npm test

# E2E Tests
npm run test:e2e

# Component Tests
npm run test:components
```

### Test-Datenbank einrichten

```bash
# SQLite Test-Datenbank erstellen
cd backend
npm run db:test:setup

# Test-Daten laden
npm run db:test:seed
```

## 🔧 Code-Qualität

### ESLint und Prettier

```bash
# Linting
npm run lint

# Formatting
npm run format

# Fix automatisch behebbare Probleme
npm run lint:fix
```

### Pre-commit Hooks

```bash
# Husky installieren
npm install --save-dev husky

# Pre-commit Hook einrichten
npx husky add .husky/pre-commit "npm run lint && npm test"
```

### TypeScript

```bash
# Type Checking
npm run type-check

# Build
npm run build
```

## 📊 Debugging

### Backend Debugging

```bash
# Debug-Modus starten
cd backend
npm run debug

# VS Code Debug-Konfiguration
# .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/backend/src/index.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "env": {
        "NODE_ENV": "development"
      },
      "runtimeArgs": ["-r", "ts-node/register"]
    }
  ]
}
```

### Frontend Debugging

```bash
# React Developer Tools installieren
# Chrome Extension: React Developer Tools

# Next.js Debug-Modus
cd frontend
npm run dev -- --inspect
```

### Database Debugging

```bash
# SQLite Browser
sqlite3 data/dashboard.db

# PostgreSQL Client
docker-compose exec postgres psql -U crowdsec -d crowdsec_dashboard

# Query Logging aktivieren
# In .env.development:
DB_LOGGING=true
```

## 🔄 API Development

### Neue API-Endpunkte hinzufügen

1. **Controller erstellen:**

```typescript
// backend/src/controllers/newController.ts
import { Request, Response } from 'express';

export class NewController {
  async getItems(req: Request, res: Response): Promise<void> {
    try {
      // Implementation
      res.json({ success: true, data: [] });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
}

export default new NewController();
```

2. **Routes registrieren:**

```typescript
// backend/src/index.ts
import newController from './controllers/newController';

// Routes
this.app.get('/api/new/items', newController.getItems.bind(newController));
```

3. **Frontend Service erweitern:**

```typescript
// frontend/src/services/api.ts
export const newService = {
  getItems: () => api.get('/new/items'),
};
```

### API-Dokumentation

```bash
# OpenAPI/Swagger Dokumentation generieren
npm run docs:generate

# Dokumentation anzeigen
npm run docs:serve
```

## 🎨 Frontend Development

### Neue Komponenten erstellen

```bash
# Component Generator (falls vorhanden)
npm run generate:component ComponentName

# Manuell erstellen
mkdir frontend/src/components/ComponentName
touch frontend/src/components/ComponentName/index.tsx
touch frontend/src/components/ComponentName/ComponentName.module.css
```

### Styling Guidelines

```typescript
// Tailwind CSS verwenden
const Component = () => (
  <div className="bg-white shadow-lg rounded-lg p-6">
    <h2 className="text-xl font-semibold text-gray-900">Title</h2>
  </div>
);

// CSS Modules für spezielle Styles
import styles from './Component.module.css';

const Component = () => (
  <div className={styles.customComponent}>
    Content
  </div>
);
```

### State Management

```typescript
// React Context für globalen State
import { createContext, useContext, useReducer } from 'react';

interface AppState {
  alerts: Alert[];
  loading: boolean;
}

const AppContext = createContext<AppState | undefined>(undefined);

export const useAppState = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppState must be used within AppProvider');
  }
  return context;
};
```

## 🔌 Integration Development

### CrowdSec Integration erweitern

```typescript
// backend/src/services/crowdsecService.ts
export class CrowdSecService {
  async getNewData(): Promise<any[]> {
    try {
      // CLI Command
      const result = await this.executeCLI(['new-command', '--format', 'json']);
      
      // API Call
      const response = await this.apiClient.get('/v1/new-endpoint');
      
      return response.data;
    } catch (error) {
      logger.error('Error fetching new data:', error);
      throw error;
    }
  }
}
```

### HAProxy Integration erweitern

```typescript
// backend/src/services/haproxyService.ts
export class HAProxyService {
  async getNewStats(): Promise<any> {
    try {
      const response = await axios.get(`${this.statsUrl}/new-endpoint`, {
        auth: this.auth,
      });
      
      return this.parseStats(response.data);
    } catch (error) {
      logger.error('Error fetching HAProxy new stats:', error);
      throw error;
    }
  }
}
```

## 📦 Build und Deployment

### Lokaler Build

```bash
# Backend Build
cd backend
npm run build

# Frontend Build
cd frontend
npm run build

# Docker Images bauen
docker build -t crowdsec-dashboard-backend ./backend
docker build -t crowdsec-dashboard-frontend ./frontend
```

### Entwicklungs-Deployment

```bash
# Staging-Umgebung
docker-compose -f docker-compose.staging.yml up -d

# Lokale Produktion testen
docker-compose -f docker-compose.prod.yml up -d
```

## 🐛 Troubleshooting

### Häufige Entwicklungsprobleme

1. **Port bereits in Verwendung:**
```bash
# Prozess finden und beenden
lsof -ti:3000 | xargs kill -9
lsof -ti:3001 | xargs kill -9
```

2. **Node Modules Probleme:**
```bash
# Cache leeren
npm cache clean --force

# Node Modules neu installieren
rm -rf node_modules package-lock.json
npm install
```

3. **TypeScript Fehler:**
```bash
# TypeScript Cache leeren
npx tsc --build --clean

# Types neu installieren
npm install --save-dev @types/node @types/express
```

4. **Database Connection Fehler:**
```bash
# SQLite Datei-Berechtigungen prüfen
ls -la data/dashboard.db

# PostgreSQL Container Status
docker-compose ps postgres
docker-compose logs postgres
```

### Debug-Logs aktivieren

```bash
# Backend Debug-Logs
DEBUG=crowdsec:* npm run dev

# Frontend Debug-Logs
NEXT_PUBLIC_DEBUG=true npm run dev
```

## 📚 Ressourcen

### Dokumentation
- [Node.js Documentation](https://nodejs.org/docs/)
- [Express.js Guide](https://expressjs.com/guide/)
- [React Documentation](https://react.dev/)
- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

### Tools
- [VS Code Extensions](https://marketplace.visualstudio.com/vscode)
- [Postman](https://www.postman.com/) für API-Tests
- [DB Browser for SQLite](https://sqlitebrowser.org/)
- [pgAdmin](https://www.pgadmin.org/) für PostgreSQL

### Community
- [CrowdSec Documentation](https://docs.crowdsec.net/)
- [HAProxy Documentation](https://docs.haproxy.org/)
- [GitHub Issues](https://github.com/your-org/crowdsec-dashboard/issues)

## 🤝 Contributing

### Pull Request Prozess

1. Fork das Repository
2. Feature Branch erstellen: `git checkout -b feature/amazing-feature`
3. Änderungen committen: `git commit -m 'Add amazing feature'`
4. Branch pushen: `git push origin feature/amazing-feature`
5. Pull Request erstellen

### Code Review Guidelines

- Code muss alle Tests bestehen
- TypeScript Typen müssen korrekt sein
- Dokumentation muss aktualisiert werden
- Performance-Impact berücksichtigen
