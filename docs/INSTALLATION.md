# Installation Guide

Dieses Dokument beschreibt die Installation und Konfiguration des CrowdSec Dashboards.

## 📋 Voraussetzungen

### System Requirements
- **Linux Server** (Ubuntu 20.04+ empfohlen)
- **Docker** >= 20.10.0
- **Docker Compose** >= 2.0.0
- **Node.js** >= 18.0.0 (für lokale Entwicklung)
- **Git** für Repository-Kloning

### CrowdSec Requirements
- **CrowdSec** >= 1.6.0 installiert und konfiguriert
- **CrowdSec Local API** aktiviert (Port 8080)
- **API Key** für Dashboard-Zugriff

### HAProxy Requirements (Optional)
- **HAProxy** >= 2.4.0
- **Stats API** aktiviert
- **CrowdSec Lua Plugin** konfiguriert

## 🚀 Installation

### 1. Repository klonen

```bash
git clone https://github.com/your-org/crowdsec-dashboard.git
cd crowdsec-dashboard
```

### 2. Umgebungsvariablen konfigurieren

```bash
# Kopiere die Beispiel-Konfiguration
cp .env.example .env

# Bearbeite die Konfiguration
nano .env
```

**Wichtige Konfigurationen:**

```bash
# Database Configuration
DB_TYPE=postgresql
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=crowdsec_dashboard
POSTGRES_USER=crowdsec
POSTGRES_PASSWORD=your_secure_password

# CrowdSec Configuration
CROWDSEC_API_URL=http://127.0.0.1:8080
CROWDSEC_API_KEY=your_crowdsec_api_key

# HAProxy Configuration (optional)
HAPROXY_STATS_URL=http://localhost:8404/stats
HAPROXY_STATS_AUTH=admin:password

# Security
JWT_SECRET=your-super-secret-jwt-key
SESSION_SECRET=your-super-secret-session-key
```

### 3. CrowdSec API Key erstellen

```bash
# Erstelle einen API Key für das Dashboard
sudo cscli bouncers add crowdsec-dashboard

# Notiere den generierten API Key
# Füge ihn in die .env Datei als CROWDSEC_API_KEY ein
```

### 4. Docker Services starten

```bash
# Alle Services im Hintergrund starten
docker-compose up -d

# Status überprüfen
docker-compose ps

# Logs verfolgen
docker-compose logs -f
```

### 5. Datenbank initialisieren

```bash
# Warte bis PostgreSQL bereit ist
docker-compose exec postgres pg_isready -U crowdsec

# Datenbank-Schema erstellen (wird automatisch beim ersten Start gemacht)
# Alternativ manuell:
docker-compose exec postgres psql -U crowdsec -d crowdsec_dashboard -f /docker-entrypoint-initdb.d/schema.sql
```

### 6. Dashboard öffnen

Öffne http://localhost:3000 in deinem Browser.

## 🔧 Konfiguration

### CrowdSec Konfiguration

#### Local API aktivieren

Stelle sicher, dass die CrowdSec Local API läuft:

```bash
# Status prüfen
sudo systemctl status crowdsec

# Local API Status
sudo cscli config show api

# Falls nicht aktiviert:
sudo cscli config set api.server.listen_uri 127.0.0.1:8080
sudo systemctl restart crowdsec
```

#### API Key Berechtigungen

```bash
# Überprüfe Bouncer-Berechtigungen
sudo cscli bouncers list

# Teste API-Zugriff
curl -H "X-Api-Key: YOUR_API_KEY" http://127.0.0.1:8080/v1/decisions
```

### HAProxy Konfiguration

#### Stats API aktivieren

Füge zu deiner HAProxy-Konfiguration hinzu:

```haproxy
# /etc/haproxy/haproxy.cfg
global
    stats socket /var/run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s

frontend stats
    bind *:8404
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
    # Optional: Basic Auth
    stats auth admin:password
```

#### CrowdSec Lua Plugin

```haproxy
# Lade das CrowdSec Lua Plugin
global
    lua-load /etc/haproxy/crowdsec.lua

frontend web
    bind *:80
    bind *:443 ssl crt /path/to/cert.pem
    
    # CrowdSec Integration
    http-request lua.crowdsec_allow
    http-request deny if { var(req.crowdsec_action) -m str "deny" }
    
    default_backend webservers
```

### PostgreSQL Konfiguration

#### Performance Tuning

```sql
-- /var/lib/postgresql/data/postgresql.conf
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

#### Backup Konfiguration

```bash
# Automatisches Backup einrichten
cat > /etc/cron.d/crowdsec-dashboard-backup << EOF
0 2 * * * postgres pg_dump -U crowdsec crowdsec_dashboard | gzip > /backup/crowdsec-dashboard-$(date +\%Y\%m\%d).sql.gz
EOF
```

## 🔒 Sicherheit

### SSL/TLS Konfiguration

#### Nginx Reverse Proxy

```nginx
# /etc/nginx/sites-available/crowdsec-dashboard
server {
    listen 443 ssl http2;
    server_name dashboard.example.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Firewall Konfiguration

```bash
# UFW Firewall Rules
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow from 127.0.0.1 to any port 3000  # Frontend (local only)
sudo ufw allow from 127.0.0.1 to any port 3001  # Backend (local only)
sudo ufw allow from 127.0.0.1 to any port 8080  # CrowdSec API (local only)
sudo ufw enable
```

## 📊 Monitoring

### Health Checks

```bash
# API Health Check
curl http://localhost:3001/health

# Database Connection
docker-compose exec postgres pg_isready -U crowdsec

# CrowdSec API
curl -H "X-Api-Key: YOUR_API_KEY" http://127.0.0.1:8080/v1/heartbeat
```

### Log Monitoring

```bash
# Dashboard Logs
docker-compose logs -f backend frontend

# CrowdSec Logs
sudo journalctl -u crowdsec -f

# HAProxy Logs
sudo tail -f /var/log/haproxy.log
```

## 🔧 Troubleshooting

### Häufige Probleme

#### 1. CrowdSec API nicht erreichbar

```bash
# Prüfe CrowdSec Status
sudo systemctl status crowdsec

# Prüfe API-Konfiguration
sudo cscli config show api

# Teste lokale Verbindung
curl http://127.0.0.1:8080/v1/heartbeat
```

#### 2. Database Connection Fehler

```bash
# Prüfe PostgreSQL Status
docker-compose exec postgres pg_isready -U crowdsec

# Prüfe Logs
docker-compose logs postgres

# Verbindung testen
docker-compose exec postgres psql -U crowdsec -d crowdsec_dashboard -c "SELECT 1;"
```

#### 3. HAProxy Stats nicht verfügbar

```bash
# Prüfe HAProxy Konfiguration
sudo haproxy -c -f /etc/haproxy/haproxy.cfg

# Teste Stats URL
curl http://localhost:8404/stats

# Mit Authentication
curl -u admin:password http://localhost:8404/stats
```

### Debug Modus

```bash
# Backend im Debug-Modus starten
docker-compose exec backend npm run dev

# Frontend im Debug-Modus
docker-compose exec frontend npm run dev

# Alle Logs anzeigen
docker-compose logs --tail=100 -f
```

## 📈 Performance Optimierung

### Database Optimierung

```sql
-- Indizes für bessere Performance
CREATE INDEX CONCURRENTLY idx_alerts_created_at ON alerts(created_at);
CREATE INDEX CONCURRENTLY idx_alerts_status ON alerts(status);
CREATE INDEX CONCURRENTLY idx_decisions_until ON decisions(until);
CREATE INDEX CONCURRENTLY idx_decisions_type_scope ON decisions(type, scope);

-- Vacuum und Analyze
VACUUM ANALYZE;
```

### Caching

```bash
# Redis Cache Status
docker-compose exec redis redis-cli info memory

# Cache leeren
docker-compose exec redis redis-cli flushall
```

## 🔄 Updates

### Dashboard Update

```bash
# Repository aktualisieren
git pull origin main

# Services neu starten
docker-compose down
docker-compose pull
docker-compose up -d

# Database Migration (falls nötig)
docker-compose exec backend npm run migrate
```

### Backup vor Update

```bash
# Database Backup
docker-compose exec postgres pg_dump -U crowdsec crowdsec_dashboard > backup-$(date +%Y%m%d).sql

# Konfiguration sichern
cp .env .env.backup
```
