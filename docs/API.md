# API Documentation

Das CrowdSec Dashboard bietet eine umfassende REST API für den Zugriff auf alle Funktionen.

## 🔗 Base URL

```
http://localhost:3001/api
```

## 🔐 Authentication

Die API verwendet JWT-Token für die Authentifizierung:

```bash
# Login (falls implementiert)
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password"}'

# API-Aufruf mit Token
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:3001/api/alerts
```

## 📊 Alerts API

### GET /api/alerts

Alle Alerts abrufen mit Pagination und Filterung.

**Query Parameters:**
- `page` (number): Seitennummer (default: 1)
- `limit` (number): Anzahl pro Seite (default: 50)
- `status` (string): Filter nach Status (`active`, `resolved`, `ignored`)
- `severity` (string): Filter nach Schweregrad (`low`, `medium`, `high`, `critical`)
- `scenario` (string): Filter nach Szenario
- `source_ip` (string): Filter nach Quell-IP
- `start_date` (string): Startdatum (ISO 8601)
- `end_date` (string): Enddatum (ISO 8601)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "crowdsec_id": "123",
      "scenario": "crowdsecurity/ssh-bf",
      "message": "Ip ************* performed 'crowdsecurity/ssh-bf' (6 events over 300s)",
      "source_ip": "*************",
      "source_country": "DE",
      "events_count": 6,
      "capacity": 5,
      "leakspeed": "10s",
      "simulated": false,
      "start_at": "2024-01-15T10:30:00Z",
      "stop_at": "2024-01-15T10:35:00Z",
      "status": "active",
      "severity": "high",
      "created_at": "2024-01-15T10:35:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 150,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### GET /api/alerts/:id

Einzelnen Alert abrufen.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "crowdsec_id": "123",
    "scenario": "crowdsecurity/ssh-bf",
    "message": "Ip ************* performed 'crowdsecurity/ssh-bf'",
    "source_ip": "*************",
    "decisions": [
      {
        "id": "decision-uuid",
        "type": "ban",
        "scope": "ip",
        "value": "*************",
        "duration": "4h",
        "until": "2024-01-15T14:35:00Z"
      }
    ]
  }
}
```

### PUT /api/alerts/:id/status

Alert-Status aktualisieren.

**Request Body:**
```json
{
  "status": "resolved"
}
```

### GET /api/alerts/stats

Alert-Statistiken abrufen.

**Query Parameters:**
- `period` (string): Zeitraum (`1h`, `24h`, `7d`, `30d`)

**Response:**
```json
{
  "success": true,
  "data": {
    "total": 150,
    "by_severity": [
      {"severity": "critical", "count": 5},
      {"severity": "high", "count": 25},
      {"severity": "medium", "count": 70},
      {"severity": "low", "count": 50}
    ],
    "by_scenario": [
      {"scenario": "crowdsecurity/ssh-bf", "count": 45},
      {"scenario": "crowdsecurity/http-bf", "count": 30}
    ],
    "hourly_trend": [
      {"hour": "2024-01-15T10:00:00Z", "count": 12},
      {"hour": "2024-01-15T11:00:00Z", "count": 8}
    ]
  }
}
```

### POST /api/alerts/sync

Alerts mit CrowdSec synchronisieren.

## 🛡️ Decisions API

### GET /api/decisions

Alle Decisions abrufen.

**Query Parameters:**
- `page`, `limit`: Pagination
- `type` (string): Decision-Typ (`ban`, `captcha`, `throttle`)
- `scope` (string): Scope (`ip`, `range`, `country`)
- `value` (string): Wert (IP, Range, etc.)
- `active_only` (boolean): Nur aktive Decisions (default: true)

### POST /api/decisions

Neue Decision erstellen.

**Request Body:**
```json
{
  "type": "ban",
  "scope": "ip",
  "value": "*************",
  "duration": "4h",
  "scenario": "manual",
  "origin": "dashboard"
}
```

### DELETE /api/decisions/:id

Decision löschen.

## 🖥️ Bouncers API

### GET /api/bouncers

Alle Bouncers abrufen.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "haproxy-bouncer",
      "type": "haproxy",
      "ip_address": "127.0.0.1",
      "version": "0.0.26",
      "last_pull": "2024-01-15T10:35:00Z",
      "is_active": true,
      "status": "online"
    }
  ]
}
```

### GET /api/bouncers/:id/metrics

Bouncer-Metriken abrufen.

**Query Parameters:**
- `period` (string): Zeitraum (`1h`, `24h`, `7d`)

## ⚙️ Engine API

### GET /api/engine/status

CrowdSec Engine Status.

**Response:**
```json
{
  "success": true,
  "data": {
    "version": "v1.6.11",
    "uptime": "72h30m15s",
    "parsers_loaded": 45,
    "scenarios_loaded": 23,
    "buckets_current": 12,
    "alerts_generated": 150,
    "decisions_created": 89
  }
}
```

### GET /api/engine/metrics

Engine-Metriken über Zeit.

## 🔄 HAProxy API

### GET /api/haproxy/stats

HAProxy Statistiken.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "pxname": "frontend_web",
      "svname": "FRONTEND",
      "scur": 15,
      "smax": 50,
      "stot": 12450,
      "bin": 1024000,
      "bout": 2048000,
      "status": "OPEN",
      "type": 0
    }
  ]
}
```

### GET /api/haproxy/metrics

Aggregierte HAProxy Metriken.

**Query Parameters:**
- `period` (string): Zeitraum

**Response:**
```json
{
  "success": true,
  "data": {
    "total": {
      "total_requests": 50000,
      "total_blocked": 1250,
      "avg_response_time": 45.2,
      "max_active_sessions": 150
    },
    "frontends": [
      {
        "frontend_name": "web",
        "requests": 25000,
        "blocked": 625,
        "avg_response_time": 42.1
      }
    ],
    "timeseries": [
      {
        "hour": "2024-01-15T10:00:00Z",
        "requests": 1200,
        "blocked": 30
      }
    ]
  }
}
```

### GET /api/haproxy/crowdsec

CrowdSec Lua Plugin Statistiken.

### GET /api/haproxy/dashboard

HAProxy Dashboard-Übersicht.

## 📈 Dashboard API

### GET /api/dashboard/stats

Dashboard-Übersichtsstatistiken.

**Response:**
```json
{
  "success": true,
  "data": {
    "alerts": {
      "total": 150,
      "active": 25,
      "last_24h": 45
    },
    "decisions": {
      "total": 89,
      "active": 67,
      "banned_ips": 45
    },
    "bouncers": {
      "total": 3,
      "online": 3,
      "offline": 0
    },
    "traffic": {
      "requests_total": 50000,
      "requests_blocked": 1250,
      "block_rate": 2.5
    }
  }
}
```

## 🔧 System API

### GET /health

System Health Check.

**Response:**
```json
{
  "success": true,
  "message": "CrowdSec Dashboard API is running",
  "version": "1.0.0",
  "timestamp": "2024-01-15T10:35:00Z"
}
```

### GET /api/system/health

Detaillierter System-Health-Check.

**Response:**
```json
{
  "success": true,
  "data": {
    "database": "connected",
    "crowdsec_api": "healthy",
    "haproxy": "healthy",
    "redis": "connected",
    "uptime": "72h30m15s"
  }
}
```

## 🔄 WebSocket Events

Das Dashboard unterstützt Real-time Updates über WebSocket:

```javascript
// Frontend JavaScript
const socket = io('ws://localhost:3001');

// Alerts-Updates abonnieren
socket.emit('join_alerts');
socket.on('alert_created', (alert) => {
  console.log('New alert:', alert);
});

// Decisions-Updates
socket.emit('join_decisions');
socket.on('decision_created', (decision) => {
  console.log('New decision:', decision);
});

// Metrics-Updates
socket.emit('join_metrics');
socket.on('metrics_updated', (metrics) => {
  console.log('Updated metrics:', metrics);
});
```

## 📝 Error Handling

Alle API-Endpunkte verwenden einheitliche Fehlerbehandlung:

**Success Response:**
```json
{
  "success": true,
  "data": { ... }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Error message",
  "details": { ... }
}
```

**HTTP Status Codes:**
- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests
- `500` - Internal Server Error

## 🔒 Rate Limiting

Die API implementiert Rate Limiting:
- **Standard**: 100 Requests pro 15 Minuten pro IP
- **Konfigurierbar** über `API_RATE_LIMIT` Environment Variable

## 📊 Pagination

Alle Listen-Endpunkte unterstützen Pagination:

**Query Parameters:**
- `page` (number): Seitennummer (1-basiert)
- `limit` (number): Anzahl pro Seite (max: 100)

**Response Format:**
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 150,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```
