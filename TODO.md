# CrowdSec Dashboard - TODO Liste

## 🏗️ Architektur Status
- ✅ **Frontend**: <PERSON><PERSON><PERSON> entfernt aus Docker - jetzt EJS Templates im Backend
- ✅ **Backend**: <PERSON><PERSON><PERSON><PERSON> in Docker Container (Port 3001)
- ✅ **Database**: PostgreSQL + Redis in Docker
- ✅ **Basic UI**: Alle Hauptseiten erstellt (Dashboard, Alerts, Decisions, Bouncers, HAProxy)

## 🔧 Backend API - Fehlende Implementierungen

### 1. CrowdSec Service Integration
- [ ] **Alerts API** - Echte CrowdSec API Integration
  - [ ] `/api/alerts` - <PERSON><PERSON>s von CrowdSec Local API abrufen
  - [ ] `/api/alerts/stats` - Alert-Statistiken berechnen
  - [ ] `/api/alerts/sync` - Manuelle Synchronisation
  - [ ] Fehlerbehandlung für CrowdSec API Ausfälle

- [ ] **Decisions API** - Echte CrowdSec API Integration
  - [ ] `/api/decisions` - Decisions von CrowdSec Local API abrufen
  - [ ] `/api/decisions/stats` - Decision-Statistiken berechnen
  - [ ] `/api/decisions/sync` - Manuelle Synchronisation
  - [ ] `/api/decisions/:id` - Einzelne Decision löschen

- [ ] **Bouncers API** - Echte CrowdSec API Integration
  - [ ] `/api/bouncers` - Bouncers von CrowdSec Local API abrufen
  - [ ] `/api/bouncers/stats` - Bouncer-Statistiken berechnen
  - [ ] `/api/bouncers/sync` - Manuelle Synchronisation
  - [ ] Bouncer-Status-Überwachung (Online/Offline)

### 2. HAProxy Integration
- [ ] **HAProxy Stats API** - Echte HAProxy Integration
  - [ ] `/api/haproxy/stats` - HAProxy Statistiken abrufen
  - [ ] `/api/haproxy/servers` - Server-Status abrufen
  - [ ] `/api/haproxy/crowdsec-status` - CrowdSec Lua Plugin Status
  - [ ] HAProxy Stats Socket Integration (falls verfügbar)

### 3. Database Services
- [ ] **Historical Data Storage**
  - [ ] Alert-Historie in PostgreSQL speichern
  - [ ] Decision-Historie in PostgreSQL speichern
  - [ ] Metrics-Historie in PostgreSQL speichern
  - [ ] Datenbereinigung (alte Daten löschen)

- [ ] **Metrics Collection**
  - [ ] Periodische Datensammlung (Cron Jobs)
  - [ ] Trend-Berechnung
  - [ ] Performance-Metriken

## 🎨 Frontend Funktionalität

### 1. Dashboard Seite
- [ ] **Real-time Updates**
  - [ ] WebSocket-Integration für Live-Daten
  - [ ] Auto-refresh alle 30 Sekunden
  - [ ] Status-Indikatoren (Online/Offline)

- [ ] **Charts und Visualisierung**
  - [ ] Alert-Trends Chart (Chart.js)
  - [ ] Decision-Trends Chart
  - [ ] HAProxy Traffic Chart
  - [ ] Threat-Level Gauge

### 2. Alerts Seite
- [ ] **Filtering und Suche**
  - [ ] Filter nach Severity (High, Medium, Low)
  - [ ] Filter nach Scenario
  - [ ] Filter nach Source IP
  - [ ] Zeitraum-Filter (24h, 7d, 30d)
  - [ ] Volltext-Suche

- [ ] **Alert Management**
  - [ ] Alert-Details Modal
  - [ ] Alert als "gelesen" markieren
  - [ ] Bulk-Aktionen (mehrere Alerts)
  - [ ] Export-Funktionalität (CSV, JSON)

### 3. Decisions Seite
- [ ] **Decision Management**
  - [ ] Decision-Details anzeigen
  - [ ] Manuelle Decision erstellen
  - [ ] Decision-Dauer ändern
  - [ ] Whitelist-Management

- [ ] **Advanced Features**
  - [ ] IP-Geolocation anzeigen
  - [ ] WHOIS-Informationen
  - [ ] Threat Intelligence Integration

### 4. Bouncers Seite
- [ ] **Bouncer Monitoring**
  - [ ] Bouncer-Details Modal
  - [ ] Performance-Metriken pro Bouncer
  - [ ] Bouncer-Konfiguration anzeigen
  - [ ] Bouncer-Logs anzeigen

### 5. HAProxy Seite
- [ ] **HAProxy Monitoring**
  - [ ] Server-Health-Checks
  - [ ] Traffic-Visualisierung
  - [ ] Response-Time-Monitoring
  - [ ] Error-Rate-Tracking

## 🔒 Sicherheit & Authentifizierung

### 1. Authentication System
- [ ] **JWT Authentication**
  - [ ] Login-Seite erstellen
  - [ ] User-Management
  - [ ] Session-Management
  - [ ] Password-Reset

- [ ] **Authorization**
  - [ ] Role-based Access Control
  - [ ] API-Key Management
  - [ ] Rate Limiting pro User

### 2. Security Features
- [ ] **Input Validation**
  - [ ] XSS-Schutz
  - [ ] SQL-Injection-Schutz
  - [ ] CSRF-Schutz

## 📊 Monitoring & Logging

### 1. Application Monitoring
- [ ] **Health Checks**
  - [ ] CrowdSec API Health Check
  - [ ] Database Health Check
  - [ ] HAProxy Health Check
  - [ ] System Resource Monitoring

### 2. Logging System
- [ ] **Structured Logging**
  - [ ] Request/Response Logging
  - [ ] Error Logging
  - [ ] Performance Logging
  - [ ] Log Rotation

## 🚀 Performance & Optimization

### 1. Caching
- [ ] **Redis Caching**
  - [ ] API Response Caching
  - [ ] Session Caching
  - [ ] Metrics Caching

### 2. Database Optimization
- [ ] **Query Optimization**
  - [ ] Database Indexing
  - [ ] Query Performance Monitoring
  - [ ] Connection Pooling

## 🧪 Testing & Quality

### 1. Testing
- [ ] **Unit Tests**
  - [ ] Service Layer Tests
  - [ ] Controller Tests
  - [ ] Utility Function Tests

- [ ] **Integration Tests**
  - [ ] API Endpoint Tests
  - [ ] Database Integration Tests
  - [ ] CrowdSec API Integration Tests

### 2. Code Quality
- [ ] **Code Standards**
  - [ ] ESLint Konfiguration
  - [ ] Prettier Konfiguration
  - [ ] TypeScript Strict Mode

## 📚 Dokumentation

### 1. API Documentation
- [ ] **OpenAPI/Swagger**
  - [ ] API Endpoints dokumentieren
  - [ ] Request/Response Schemas
  - [ ] Authentication Documentation

### 2. User Documentation
- [ ] **User Guide**
  - [ ] Dashboard-Bedienung
  - [ ] Feature-Erklärungen
  - [ ] Troubleshooting Guide

## 🔧 DevOps & Deployment

### 1. CI/CD Pipeline
- [ ] **GitHub Actions**
  - [ ] Automated Testing
  - [ ] Docker Build & Push
  - [ ] Deployment Automation

### 2. Production Readiness
- [ ] **Environment Configuration**
  - [ ] Production Environment Variables
  - [ ] SSL/TLS Configuration
  - [ ] Reverse Proxy Setup (Nginx/HAProxy)

## 🎯 Priorität

### Hoch (Sofort)
1. CrowdSec API Integration (Alerts, Decisions, Bouncers)
2. HAProxy Stats Integration
3. Real-time Updates (WebSocket)
4. Basic Authentication

### Mittel (Nächste Woche)
1. Historical Data Storage
2. Charts und Visualisierung
3. Filtering und Suche
4. Health Checks

### Niedrig (Später)
1. Advanced Security Features
2. Performance Optimization
3. Comprehensive Testing
4. CI/CD Pipeline

---

**Status**: Frontend ist NICHT mehr in Docker - wurde durch EJS Templates im Backend ersetzt!
**Aktueller Stand**: Basic UI funktioniert, APIs müssen noch implementiert werden.
